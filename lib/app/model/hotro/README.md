# UnitTreeNode Model - <PERSON><PERSON>ớng dẫn sử dụng

## Tổng quan
`UnitTreeNode` là model để quản lý cấu trúc cây đơn vị trong ứng dụng. Model này hỗ trợ:
- <PERSON>y<PERSON>n đổi từ/sang JSON để tương tác với API
- Tương thích với jsTree format
- Quản lý trạng thái expanded/selected với GetX reactive

## Cấu trúc Model

```dart
class UnitTreeNode {
  final int id;              // ID của đơn vị
  final String name;         // Tên đơn vị
  final int? parentId;       // ID đơn vị cha (null nếu là root)
  final List<UnitTreeNode> children;  // Danh sách đơn vị con
  final RxBool expanded;     // Trạng thái mở rộng (reactive)
  final RxBool selected;     // Trạng thái được chọn (reactive)
}
```

## Các Method chính

### 1. fromJson(Map<String, dynamic> json)
Tạo UnitTreeNode từ JSON thông thường:

```dart
final json = {
  'id': 1,
  'name': 'Phòng Công nghệ thông tin',
  'parentId': 0,
  'expanded': true,
  'selected': false,
  'children': [...]
};

final unit = UnitTreeNode.fromJson(json);
```

### 2. toJson()
Chuyển UnitTreeNode thành JSON:

```dart
final unit = UnitTreeNode(id: 1, name: 'IT Department');
final json = unit.toJson();
// Kết quả:
// {
//   'id': 1,
//   'name': 'IT Department',
//   'text': 'IT Department',
//   'parentId': null,
//   'parent': '#',
//   'expanded': false,
//   'selected': false,
//   'children': [],
//   'state': {'opened': false, 'selected': false}
// }
```

### 3. fromJsTree(Map<String, dynamic> json)
Tạo UnitTreeNode từ jsTree format (thường từ API):

```dart
final jsTreeJson = {
  'id': '1',
  'text': 'Phòng Công nghệ thông tin',
  'parent': '0',
  'state': {
    'opened': true,
    'selected': false
  }
};

final unit = UnitTreeNode.fromJsTree(jsTreeJson);
```

### 4. toJsTree()
Chuyển UnitTreeNode thành jsTree format (để gửi lên API):

```dart
final unit = UnitTreeNode(id: 1, name: 'IT Department');
final jsTreeJson = unit.toJsTree();
// Kết quả:
// {
//   'id': '1',
//   'text': 'IT Department',
//   'parent': '#',
//   'state': {'opened': false, 'selected': false}
// }
```

### 5. copyWith()
Tạo bản sao với một số thuộc tính được thay đổi:

```dart
final original = UnitTreeNode(id: 1, name: 'Original');
final updated = original.copyWith(
  name: 'Updated Name',
  expanded: true
);
```

## Ví dụ sử dụng với API

### 1. Lấy dữ liệu từ API
```dart
Future<List<UnitTreeNode>> getUnitsFromApi() async {
  final response = await http.get('/api/units');
  final data = jsonDecode(response.body);
  
  if (data['success']) {
    final unitsList = data['data'] as List;
    return unitsList.map((json) => UnitTreeNode.fromJsTree(json)).toList();
  }
  
  return [];
}
```

### 2. Gửi dữ liệu lên API
```dart
Future<bool> saveUnitToApi(UnitTreeNode unit) async {
  final jsonData = unit.toJsTree();
  
  final response = await http.post(
    '/api/units',
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode(jsonData),
  );
  
  return response.statusCode == 200;
}
```

### 3. Xây dựng cây từ danh sách phẳng
```dart
UnitTreeNode buildTreeFromFlatList(List<dynamic> flatList) {
  Map<String, UnitTreeNode> nodeMap = {};
  UnitTreeNode? rootNode;

  // Tạo tất cả các node
  for (var item in flatList) {
    final node = UnitTreeNode.fromJsTree(item);
    nodeMap[node.id.toString()] = node;
    
    if (item['parent'] == '#') {
      rootNode = node;
    }
  }

  // Xây dựng cấu trúc cây
  for (var item in flatList) {
    final nodeId = item['id'].toString();
    final parentId = item['parent'].toString();
    
    if (parentId != '#' && nodeMap.containsKey(parentId)) {
      final parentNode = nodeMap[parentId]!;
      final childNode = nodeMap[nodeId]!;
      
      final updatedChildren = List<UnitTreeNode>.from(parentNode.children)
        ..add(childNode);
      final updatedParent = parentNode.copyWith(children: updatedChildren);
      nodeMap[parentId] = updatedParent;
    }
  }

  return rootNode!;
}
```

## Lưu ý quan trọng

1. **Reactive Properties**: `expanded` và `selected` là RxBool, cần sử dụng `.value` để truy cập giá trị
2. **ID Format**: API có thể trả về ID dạng string, model sẽ tự động chuyển đổi sang int
3. **Parent Reference**: Hỗ trợ cả `parentId` (int) và `parent` (string) format
4. **jsTree Compatibility**: Các method `fromJsTree` và `toJsTree` tương thích với thư viện jsTree
5. **Immutable Children**: List children là immutable, sử dụng `copyWith` để cập nhật

## Ví dụ hoàn chỉnh

Xem file `demo_api_usage.dart` để có ví dụ chi tiết về cách sử dụng tất cả các tính năng.
