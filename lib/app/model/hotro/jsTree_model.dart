// Model cho đơn vị dạng cây
import 'package:get/get.dart';

class UnitTreeNode {
  final int id;
  final String name;
  final int? parentId;
  final List<UnitTreeNode> children;
  final RxBool expanded;
  final RxBool selected;

  UnitTreeNode({
    required this.id,
    required this.name,
    this.parentId,
    this.children = const [],
    bool expanded = false,
    bool selected = false,
  })  : expanded = expanded.obs,
        selected = selected.obs;
}
