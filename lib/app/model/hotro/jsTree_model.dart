// Model cho đơn vị dạng cây
import 'package:get/get.dart';

class UnitTreeNode {
  final int id;
  final String name;
  final int? parentId;
  final List<UnitTreeNode> children;
  final RxBool expanded;
  final RxBool selected;

  UnitTreeNode({
    required this.id,
    required this.name,
    this.parentId,
    this.children = const [],
    bool expanded = false,
    bool selected = false,
  })  : expanded = expanded.obs,
        selected = selected.obs;

  // Factory constructor để tạo UnitTreeNode từ JSON
  factory UnitTreeNode.fromJson(Map<String, dynamic> json) {
    return UnitTreeNode(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] ?? 0,
      name: json['name'] ?? json['text'] ?? '',
      parentId: json['parentId'] != null
          ? (json['parentId'] is String
              ? int.parse(json['parentId'])
              : json['parentId'])
          : (json['parent'] != null && json['parent'] != '#'
              ? (json['parent'] is String
                  ? int.parse(json['parent'])
                  : json['parent'])
              : null),
      expanded: json['expanded'] ?? false,
      selected: json['selected'] ?? false,
      children: json['children'] != null
          ? (json['children'] as List)
              .map((child) => UnitTreeNode.fromJson(child))
              .toList()
          : [],
    );
  }

  // Method để chuyển UnitTreeNode thành JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'text': name, // Thêm text field cho tương thích với jsTree
      'parentId': parentId,
      'parent': parentId?.toString() ??
          '#', // Thêm parent field cho tương thích với jsTree
      'expanded': expanded.value,
      'selected': selected.value,
      'children': children.map((child) => child.toJson()).toList(),
      'state': {
        'opened': expanded.value,
        'selected': selected.value,
      },
    };
  }

  // Method để tạo UnitTreeNode từ jsTree format
  factory UnitTreeNode.fromJsTree(Map<String, dynamic> json) {
    return UnitTreeNode(
      id: json['id'] is String ? int.parse(json['id']) : json['id'] ?? 0,
      name: json['text'] ?? '',
      parentId: json['parent'] != null && json['parent'] != '#'
          ? (json['parent'] is String
              ? int.parse(json['parent'])
              : json['parent'])
          : null,
      expanded: json['state']?['opened'] ?? false,
      selected: json['state']?['selected'] ?? false,
    );
  }

  // Method để chuyển thành jsTree format
  Map<String, dynamic> toJsTree() {
    return {
      'id': id.toString(),
      'text': name,
      'parent': parentId?.toString() ?? '#',
      'state': {
        'opened': expanded.value,
        'selected': selected.value,
      },
    };
  }

  // Method để copy UnitTreeNode với các thuộc tính mới
  UnitTreeNode copyWith({
    int? id,
    String? name,
    int? parentId,
    List<UnitTreeNode>? children,
    bool? expanded,
    bool? selected,
  }) {
    return UnitTreeNode(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      children: children ?? this.children,
      expanded: expanded ?? this.expanded.value,
      selected: selected ?? this.selected.value,
    );
  }
}
