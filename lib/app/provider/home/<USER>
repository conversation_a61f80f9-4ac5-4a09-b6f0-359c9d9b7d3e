import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/login/ds_nhacviec_canbo.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/login_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_default.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';

class HomeProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();
  Future<List<DsNhacViecCb>> auDsNVCB(
      int maCtcbKc, String? tuNgay, String? denNgay) async {
    DateTime now = DateTime.now();
    denNgay ??= DateFormat('dd/MM/yyyy').format(now);
    String htThoiGianNhacViec =
        _store.read(GetStorageKey.tsHtChonTgianNhacViec);
    int month = int.parse(htThoiGianNhacViec.substring(0, 1));

    if (htThoiGianNhacViec == "all" || htThoiGianNhacViec == "hiden") {
      tuNgay = "01/01/2000";
    } else {
      DateTime endDate = MethodUntils.subtractMonths(now, month);
      tuNgay = DateFormat('dd/MM/yyyy').format(endDate);
    }
    try {
      var response = await dio.get(
          '${LoginApi.auNVDB}?ma_ctcb=$maCtcbKc&tu_ngay=$tuNgay&den_ngay=$denNgay');
      List<DsNhacViecCb> listNv = [];
      for (var item in response.data) {
        listNv.add(DsNhacViecCb.fromJson(item));
      }
      return listNv;
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<dynamic> jsTreeDonViChuQuan(int maCtcbKc) async {
    try {
      final response =
          await dio.get('${ApiDefault.aujsTreeDonVi}?ma_ctcb=$maCtcbKc');
      return response.data;
    } on DioError catch (err) {
      final erroMesage = dioError.DioException.fromDioError(err).toString();
      return Future.error(erroMesage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
