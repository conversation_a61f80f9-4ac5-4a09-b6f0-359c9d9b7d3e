import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/tracuu/tracuu_vbde_model.dart';
import 'package:vnpt_ioffice_camau/app/model/tracuu/tracuu_vbdi_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/api/tracuu_api.dart';
import 'package:vnpt_ioffice_camau/app/provider/api_provider.dart';
import 'package:vnpt_ioffice_camau/app/provider/dio_exception.dart' as dioError;

import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';

class TraCuuProvider {
  final dio = ApiRoot().dio;
  final GetStorage _store = GetStorage();
  // tra cứu văn bản đi
  Future<MdTraCuuVanBanDi> auVbdiTraCuu(int page, int size, String vaiTro,
      String trichYeu, String soKyHieu, int nam) async {
    try {
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

      var tuNgay = '01/01/$nam';
      var denNgay = '31/12/$nam';
      var data = FormData.fromMap({
        'trich_yeu': trichYeu,
        'so_ky_hieu': soKyHieu,
        'den_ngay': denNgay,
        'ma_ctcb': maCtcb,
        'ma_don_vi_quan_tri': maDonViQuanTri,
        'ma_linh_vuc_van_ban': 0,
        'ma_loai_van_ban': 0,
        'ma_so_van_ban': 0,
        'nguoi_ky': 'ALL',
        'page': page,
        'size': size,
        'phong_ban': 0,
        'tim_chinh_xac': 0,
        'tim_cx': 'fase',
        'trang_thai': 1,
        'tu_ngay': tuNgay,
        'vai_tro': vaiTro
      });
      final response = await dio.post(TraCuuApi.auTraCuuVbdi, data: data);
      return MdTraCuuVanBanDi.fromJson(response.data);
      // ignore: deprecated_member_use
    } on DioError catch (err) {
      final errorMessage = dioError.DioException.fromDioError(err).toString();
      return Future.error(errorMessage);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }

  Future<MdTraCuuVanBanDen> auVbdeTraCuu(
      int size, int page, String trichYeu, String soKyHieu, int nam) async {
    try {
      int maCtcb = _store.read(GetStorageKey.maCtcbKc);
      int maDonViQuanTri = _store.read(GetStorageKey.maDonViQuanTri);

      var tuNgay = '01/01/$nam';
      var denNgay = '31/12/$nam';
      var data = FormData.fromMap({
        'trich_yeu': trichYeu,
        'so_ky_hieu': soKyHieu,
        'den_ngay': denNgay,
        'ma_ctcb': maCtcb,
        'ma_ctcb_duyet': 0,
        'ma_don_vi_quan_tri': maDonViQuanTri,
        'ma_loai_van_ban': 0,
        'ma_so_van_ban': 0,
        'page': page,
        'size': size,
        'tim_chinh_xac': 0,
        'tim_cx': false,
        'tu_ngay': tuNgay
      });
      final response = await dio.post(TraCuuApi.auTraCuuVbde, data: data);
      return MdTraCuuVanBanDen.fromJson(response.data);
    } catch (exception) {
      return Future.error(exception.toString());
    }
  }
}
