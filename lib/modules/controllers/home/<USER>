import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/app/model/login/ds_nhacviec_canbo.dart'
    as _nv;
import 'package:vnpt_ioffice_camau/app/model/login/ds_nhacviec_canbo.dart';
import 'package:vnpt_ioffice_camau/app/model/user/ctcb_kiemnhiem.dart';
import 'package:vnpt_ioffice_camau/app/provider/home/<USER>';
import 'package:vnpt_ioffice_camau/app/provider/login/login_provider.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/login/login_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class HomeController extends GetxController {
  final _getStorage = GetStorage();
  var loginProvider = LoginProvider();
  var homeTile = "this home".obs;
  var nvChoDuyet = 0.obs;
  var nvVbdeXuly = 0.obs;
  var nvVbdiChoDuyet = 0.obs;
  var nvVbdiXuLy = 0.obs;
  var nvTTDH = 0.obs;
  var nvVbNoiBo = 0.obs;
  var nvSoVbde = 0.obs;
  var isLanhDao = 0.obs;
  var isVanThu = 0.obs;
  var isChuyenVien = 0.obs;
  var nvtheoDoiVb = 0.obs;
  RxList<DsCtcbKiemNhiem> dsChucVuKiemNhiem = <DsCtcbKiemNhiem>[].obs;
  var selectChucVu = "".obs;

  var arrDsNhacViec = <ItemNhacViec>[].obs;
  List<int> badgeNhacViec = [0, 0, 0, 0, 0, 0, 0, 0].obs;
  void loadDSnv() async {
    arrDsNhacViec.clear();
    isLanhDao.value = await _getStorage.read(GetStorageKey.isLanhDao);
    isVanThu.value = await _getStorage.read(GetStorageKey.isVanThu);
    isChuyenVien.value = await _getStorage.read(GetStorageKey.isChuyenVien);
    arrDsNhacViec.value = ([
      if (_getStorage.read(GetStorageKey.isVanThu) == 1)
        ItemNhacViec.fromJson({
          "key": "vbdeVanThu",
          "tenNhacViec": "Sổ văn bản đến",
          "indexNhacViec": 0,
          "iconSvg": "svg/archive.svg",
        }),
      if (_getStorage.read(GetStorageKey.isLanhDao) == 1)
        ItemNhacViec.fromJson({
          "key": "vbdeDuyet",
          "tenNhacViec": "Duyệt văn bản đến",
          "indexNhacViec": 1,
          "iconSvg": "svg/duyetvbde.svg"
        }),
      ItemNhacViec.fromJson({
        "key": "vbdeXuLy",
        "tenNhacViec": "Xử lý văn bản đến",
        "indexNhacViec": 2,
        "iconSvg": "svg/xulyvbde.svg"
      }),
      ItemNhacViec.fromJson({
        "key": "vbdeTheoDoi",
        "tenNhacViec": "Văn bản đến theo dõi",
        "indexNhacViec": 3,
        "iconSvg": "svg/theodoivb.svg"
      }),
      if (_getStorage.read(GetStorageKey.isLanhDao) == 1)
        ItemNhacViec.fromJson({
          "key": "vbdiDuyet",
          "tenNhacViec": "Duyệt văn bản đi",
          "indexNhacViec": 4,
          "iconSvg": "svg/duyetvbdi.svg"
        }),
      ItemNhacViec.fromJson({
        "key": "vbdiXuLy",
        "tenNhacViec": "Xử lý văn bản đi",
        "isShow": true,
        "indexNhacViec": 5,
        "iconSvg": "svg/xulyvbdi.svg"
      }),
      ItemNhacViec.fromJson({
        "key": "TTDH",
        "tenNhacViec": "Thông tin điều hành",
        "indexNhacViec": 6,
        "iconSvg": "svg/thongtindieuhanh.svg"
      }),
      ItemNhacViec.fromJson({
        "key": "VBNB",
        "tenNhacViec": "Văn bản nội bộ",
        "indexNhacViec": 7,
        "iconSvg": "svg/vanbannoibo.svg"
      }),
      ItemNhacViec.fromJson({
        "key": "LCT",
        "tenNhacViec": "Lịch công tác",
        "indexNhacViec": 8,
        "iconSvg": "svg/calendar.svg"
      }),
    ]);
    HomeProvider()
        .auDsNVCB(_getStorage.read(GetStorageKey.maCtcbKc), null, null)
        .then((item) {
      nvSoVbde.value = MethodUntils.getNvNew("vbde",
          "van-ban-den/so-van-ban-den?page=1&t=so_vb_den_dien_tu_cua_vt", item);
      nvChoDuyet.value = MethodUntils.getNvNew(
          "vbde",
          "van-ban-den/duyet-van-ban-den?page=1&t=vb_den_cho_duyet_cua_ld",
          item);
      nvVbdeXuly.value = MethodUntils.getNvNew(
              "vbde", "van-ban-den/van-ban-den-chua-xu-ly?page=1", item) +
          MethodUntils.getNvNew(
              "vbde", "van-ban-den/van-ban-den-dang-xu-ly?page=1", item);
      nvVbdiChoDuyet.value = MethodUntils.getNvNew("vbdi",
          "van-ban-di/duyet-van-ban-di?page=1&t=vb_di_cho_duyet_cua_ld", item);
      nvVbdiXuLy.value = MethodUntils.getNvNew(
          "vbdi", "van-ban-di/van-ban-di-cho-xu-ly?page=1", item);
      nvTTDH.value = MethodUntils.getNvNew(
          "khac", "thong-diep/thong-tin-dieu-hanh-chua-xem?page=1", item);
      nvtheoDoiVb.value = MethodUntils.getNvNew(
          "vbde", "van-ban-den/van-ban-den-theo-doi-xu-ly?page=1", item);

      badgeNhacViec.clear();
      badgeNhacViec.addAll([
        if (_getStorage.read(GetStorageKey.isVanThu) == 1)
          nvSoVbde.value, // sổ văn bản đến
        if (_getStorage.read(GetStorageKey.isLanhDao) == 1)
          nvChoDuyet.value, // duyệt văn bản đến
        nvVbdeXuly.value, // xử lý văn bản đến
        nvtheoDoiVb.value,
        if (_getStorage.read(GetStorageKey.isLanhDao) == 1)
          nvVbdiChoDuyet.value, // duyệt văn bản đi
        nvVbdiXuLy.value, // xử lý văn bản đi
        nvTTDH.value, // thông tin điều hành
        nvVbNoiBo.value, // văn bản nội bộ
        0 // lịch công tác
      ]);
      selectChucVu.value = _getStorage.read(GetStorageKey.maCtcbKc).toString();
    }, onError: (error) {
      CustomSnackBar.showErrorSnackBar(
          context: Get.context,
          title: AppString.error,
          message: error.toString());
    });
  }

  void getTokenFCM() async {
    await FirebaseMessaging.instance.getToken().then((token) => {
          print(token),
          // storage.write(GetStorageKey.accessTokenFCM, token),
          saveTokenFCM(token!)
        });
  }

  void saveTokenFCM(String token) async {
    await loginProvider.auFCM(token).then((value) => {print(value)});
  }

  void onRefreshTokenFCM() {
    FirebaseMessaging.instance.onTokenRefresh.listen((fmcToken) {
      saveTokenFCM(fmcToken);
      // storage.write(GetStorageKey.accessTokenFCM, fmcToken);
    });
  }

  // void login change chức vự
  void getDsKiemNhiem() async {
    selectChucVu.value = _getStorage.read(GetStorageKey.maCtcbKc).toString();
    await loginProvider.auDsCtcbKiemNhiem().then((value) {
      if (value.data!.isNotEmpty) {
        dsChucVuKiemNhiem.addAll(value.data!);
        dsChucVuKiemNhiem.isNotEmpty
            ? selectChucVu.value =
                dsChucVuKiemNhiem[0].maCtcbKc!.toInt().toString()
            : selectChucVu;
      }
    });
  }

  void changeMaCtbKiemNhiem(String maCtcb) {
    DsCtcbKiemNhiem itemChucVu = dsChucVuKiemNhiem.value
        .where((element) => element.maCtcbKc!.toInt().toString() == maCtcb)
        .first;
    selectChucVu.value = maCtcb;
    Get.defaultDialog(
      title: "Thông báo",
      middleText: "Bạn muốn đổi kiêm nhiệm sang ${itemChucVu.tenChucVu}",
      textCancel: "Đóng",
      textConfirm: "Ok!",
      confirmTextColor: Colors.white,
      onConfirm: () {
        onPressChangeCv(itemChucVu);
      },
      onCancel: () {
        Get.back();
      },
    );
  }

  void onPressChangeCv(DsCtcbKiemNhiem item) async {
    await loginProvider.auDelFCM().then((value) {
      _getStorage.write(GetStorageKey.maCtcbKcStore, item.maCtcbKc!.toInt());
      Get.offAllNamed(Routers.LOGIN,
          arguments: {"loaiDangNhap": "doikiemnhiem"});
      Get.delete();
    });
  }

  @override
  void onInit() {
    super.onInit();
    getDsKiemNhiem();

    getTokenFCM();
  }

  @override
  void onReady() {
    loadDSnv();
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    arrDsNhacViec.clear();
    badgeNhacViec.clear();
  }

  @override
  void dispose() {
    super.dispose();
    arrDsNhacViec.close();
  }
}
