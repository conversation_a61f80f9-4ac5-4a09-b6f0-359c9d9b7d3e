import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HoTroController extends GetxController {
  final TextEditingController searchController = TextEditingController();
  final TextEditingController unitSearchController = TextEditingController();
  Rx<int> selectedUnitId = 0.obs;
  List<Map<String, dynamic>> documentList = [];
  Rx<bool> isSearching = false.obs;
  Rx<bool> isRecalling = false.obs;
  Rx<int> recallingDocId = 0.obs;
  bool showUnitDropdown = false;
  String selectedUnitName = '';

  Rx<bool> isUnitSearchVisible = false.obs;

  List<Map<String, dynamic>> filteredUnits = [];
  // Danh sách đơn vị

  //List<Map<String, dynamic>> flattenedUnits = [];

  // Danh sách đơn vị theo cấu trúc cha-con (mẫu)
  final List<Map<String, dynamic>> units = [
    {
      'id': 1,
      'name': 'UBND Tỉnh Cà Mau',
      'parentId': null,
      'level': 0,
      'children': [
        {
          'id': 11,
          'name': 'Văn phòng UBND',
          'parentId': 1,
          'level': 1,
          'children': [
            {
              'id': 111,
              'name': 'Phòng Hành chính',
              'parentId': 11,
              'level': 2,
              'children': []
            },
            {
              'id': 112,
              'name': 'Phòng Pháp chế',
              'parentId': 11,
              'level': 2,
              'children': []
            },
          ]
        },
        {
          'id': 12,
          'name': 'Sở Nội vụ',
          'parentId': 1,
          'level': 1,
          'children': [
            {
              'id': 121,
              'name': 'Phòng Tổ chức cán bộ',
              'parentId': 12,
              'level': 2,
              'children': []
            },
            {
              'id': 122,
              'name': 'Phòng Chính sách',
              'parentId': 12,
              'level': 2,
              'children': []
            },
          ]
        },
        {
          'id': 13,
          'name': 'Sở Tài chính',
          'parentId': 1,
          'level': 1,
          'children': [
            {
              'id': 131,
              'name': 'Phòng Ngân sách',
              'parentId': 13,
              'level': 2,
              'children': []
            },
            {
              'id': 132,
              'name': 'Phòng Kế toán',
              'parentId': 13,
              'level': 2,
              'children': []
            },
          ]
        }
      ]
    }
  ];

  // List<Map<String, dynamic>> get flattenedUnits {
  //   List<Map<String, dynamic>> result = [];

  //   void addUnitsRecursively(List<Map<String, dynamic>> unitList) {
  //     for (var unit in unitList) {
  //       result.add(unit);
  //       if (unit['children'] != null && unit['children'].isNotEmpty) {
  //         addUnitsRecursively(unit['children']);
  //       }
  //     }
  //   }

  //   addUnitsRecursively(units);
  //   return result;
  // }

  void selectUnit(int unitId, String unitName) {
    selectedUnitId.value = unitId;
    selectedUnitName = unitName;
    showUnitDropdown = false;
    documentList.clear();
    searchController.clear();
    unitSearchController.clear();
    filteredUnits = List.from(flattenedUnits);
  }

  // Dữ liệu văn bản mẫu theo đơn vị
  final Map<int, List<Map<String, dynamic>>> documentData = {
    1: [
      {
        'id': 1,
        'kyHieu': '01/QĐ-UBND',
        'tieuDe': 'Quyết định về việc phân công nhiệm vụ năm 2024',
        'ngayBanHanh': '15/01/2024',
        'loaiVanBan': 'Quyết định',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Chủ tịch UBND'
      },
      {
        'id': 2,
        'kyHieu': '02/CV-UBND',
        'tieuDe': 'Công văn về triển khai kế hoạch công tác',
        'ngayBanHanh': '20/01/2024',
        'loaiVanBan': 'Công văn',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Phó Chủ tịch UBND'
      },
      {
        'id': 3,
        'kyHieu': '03/TB-UBND',
        'tieuDe': 'Thông báo về lịch họp tháng 2/2024',
        'ngayBanHanh': '25/01/2024',
        'loaiVanBan': 'Thông báo',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Chánh Văn phòng'
      },
    ],
    2: [
      {
        'id': 4,
        'kyHieu': '15/QĐ-NV',
        'tieuDe': 'Quyết định bổ nhiệm cán bộ',
        'ngayBanHanh': '10/02/2024',
        'loaiVanBan': 'Quyết định',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Trưởng phòng Nội vụ'
      },
      {
        'id': 5,
        'kyHieu': '16/CV-NV',
        'tieuDe': 'Công văn đề nghị tăng cường kỷ luật',
        'ngayBanHanh': '12/02/2024',
        'loaiVanBan': 'Công văn',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Phó Trưởng phòng'
      },
    ],
    3: [
      {
        'id': 6,
        'kyHieu': '25/QĐ-TCKH',
        'tieuDe': 'Quyết định phân bổ ngân sách năm 2024',
        'ngayBanHanh': '01/03/2024',
        'loaiVanBan': 'Quyết định',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Trưởng phòng TC-KH'
      },
    ],
    4: [
      {
        'id': 7,
        'kyHieu': '30/CV-TNMT',
        'tieuDe': 'Công văn về bảo vệ môi trường',
        'ngayBanHanh': '05/03/2024',
        'loaiVanBan': 'Công văn',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Trưởng phòng TNMT'
      },
    ],
    5: [
      {
        'id': 8,
        'kyHieu': '40/QĐ-KTHT',
        'tieuDe': 'Quyết định đầu tư cơ sở hạ tầng',
        'ngayBanHanh': '10/03/2024',
        'loaiVanBan': 'Quyết định',
        'trangThai': 'Đã ban hành',
        'nguoiKy': 'Trưởng phòng KT-HT'
      },
    ],
  };

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  void searchUnits(String query) {
    if (query.isEmpty) {
      filteredUnits = List.from(flattenedUnits);
    } else {
      filteredUnits = flattenedUnits.where((unit) {
        return unit['name']
            .toString()
            .toLowerCase()
            .contains(query.toLowerCase());
      }).toList();
    }
  }

  List<Map<String, dynamic>> get flattenedUnits {
    List<Map<String, dynamic>> result = [];

    void addUnitsRecursively(List<Map<String, dynamic>> unitList) {
      for (var unit in unitList) {
        result.add(unit);
        if (unit['children'] != null && unit['children'].isNotEmpty) {
          addUnitsRecursively(unit['children']);
        }
      }
    }

    addUnitsRecursively(units);
    return result;
  }

  Future<void> searchDocuments() async {
    if (selectedUnitId.value == 0) {
      Get.snackbar(
        'Thông báo',
        'Vui lòng chọn đơn vị trước khi tìm kiếm!',
        backgroundColor: Colors.orange[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    isSearching.value = true;

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    String searchTerm = searchController.text.trim().toLowerCase();
    List<Map<String, dynamic>> allDocs = documentData[selectedUnitId] ?? [];

    if (searchTerm.isEmpty) {
      documentList = List.from(allDocs);
    } else {
      documentList = allDocs.where((doc) {
        return doc['kyHieu'].toString().toLowerCase().contains(searchTerm) ||
            doc['tieuDe'].toString().toLowerCase().contains(searchTerm);
      }).toList();
    }
    isSearching.value = false;

    if (documentList.isEmpty && searchTerm.isNotEmpty) {
      Get.snackbar(
        'Thông báo',
        'Không tìm thấy văn bản với ký hiệu "$searchTerm"',
        backgroundColor: Colors.blue[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  Future<void> recallDocument(Map<String, dynamic> document) async {
    isRecalling.value = true;
    recallingDocId.value = document['id'];

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Thực hiện thu hồi văn bản ở đây
      // await apiService.recallDocument(document['id']);

      Get.snackbar(
        'Thành công',
        'Đã thu hồi văn bản "${document['kyHieu']}" thành công!',
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );

      // Cập nhật trạng thái văn bản

      int index = documentList.indexWhere((doc) => doc['id'] == document['id']);
      if (index != -1) {
        documentList[index]['trangThai'] = 'Đã thu hồi';
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể thu hồi văn bản "${document['kyHieu']}". Vui lòng thử lại!',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isRecalling.value = false;
      recallingDocId.value = 0;
    }
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }
}
