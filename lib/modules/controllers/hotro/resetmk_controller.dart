import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetMkNguoiDungController extends GetxController {
  // Thêm vào UserController class

// Observable variables
  var selectedUnitId = 0.obs;
  var staffList = <Map<String, dynamic>>[].obs;
  var isResetting = false.obs;
  var resettingStaffId = 0.obs;

// Danh sách đơn vị
  final units = [
    {'id': 1, 'name': 'Phòng Công nghệ thông tin'},
    {'id': 2, 'name': 'Phòng Hành chính - Nhân sự'},
    {'id': 3, 'name': 'Phòng Tài chính - Kế toán'},
    {'id': 4, 'name': 'Phòng Kinh doanh'},
    {'id': 5, 'name': '<PERSON>òng Kỹ thuật'},
  ];

// Dữ liệu cán bộ theo đơn vị (có thể thay bằng API call)
  final Map<int, List<Map<String, dynamic>>> staffData = {
    1: [
      {
        'id': 1,
        'name': '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
        'position': 'Trưởng phòng',
        'code': 'IT001'
      },
      {
        'id': 2,
        'name': 'Trần Thị Bình',
        'position': 'Chuyên viên',
        'code': 'IT002'
      },
      {
        'id': 3,
        'name': 'Lê Văn Cường',
        'position': 'Nhân viên',
        'code': 'IT003'
      },
    ],
    2: [
      {
        'id': 4,
        'name': 'Phạm Thị Dung',
        'position': 'Trưởng phòng',
        'code': 'HR001'
      },
      {
        'id': 5,
        'name': 'Vũ Văn Em',
        'position': 'Chuyên viên',
        'code': 'HR002'
      },
    ],
    3: [
      {
        'id': 6,
        'name': 'Hoàng Thị Giang',
        'position': 'Trưởng phòng',
        'code': 'FN001'
      },
      {
        'id': 7,
        'name': 'Đỗ Văn Hùng',
        'position': 'Kế toán viên',
        'code': 'FN002'
      },
    ],
    4: [
      {
        'id': 8,
        'name': 'Bùi Thị Lan',
        'position': 'Trưởng phòng',
        'code': 'BD001'
      },
      {
        'id': 9,
        'name': 'Ngô Văn Minh',
        'position': 'Nhân viên kinh doanh',
        'code': 'BD002'
      },
    ],
    5: [
      {
        'id': 10,
        'name': 'Tạ Thị Nga',
        'position': 'Trưởng phòng',
        'code': 'TC001'
      },
      {'id': 11, 'name': 'Lý Văn Ơn', 'position': 'Kỹ sư', 'code': 'TC002'},
    ],
  };

// Methods
  void selectUnit(int unitId) {
    selectedUnitId.value = unitId;
    if (unitId > 0 && staffData.containsKey(unitId)) {
      staffList.value = staffData[unitId]!;
    } else {
      staffList.clear();
    }
  }

  Future<void> resetPassword(Map<String, dynamic> staff) async {
    try {
      isResetting.value = true;
      resettingStaffId.value = staff['id'];

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Thực hiện reset password ở đây
      // await apiService.resetPassword(staff['id']);

      Get.snackbar(
        'Thành công',
        'Đã reset mật khẩu thành công cho ${staff['name']}!',
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể reset mật khẩu cho ${staff['name']}. Vui lòng thử lại!',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isResetting.value = false;
      resettingStaffId.value = 0;
    }
  }

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() {
    // TODO: implement onReady
    super.onReady();
  }
}
