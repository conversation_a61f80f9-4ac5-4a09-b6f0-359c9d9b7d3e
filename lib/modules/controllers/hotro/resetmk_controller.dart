import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Model cho đơn vị dạng cây
class UnitTreeNode {
  final int id;
  final String name;
  final int? parentId;
  final List<UnitTreeNode> children;
  final RxBool expanded;
  final RxBool selected;

  UnitTreeNode({
    required this.id,
    required this.name,
    this.parentId,
    this.children = const [],
    bool expanded = false,
    bool selected = false,
  })  : expanded = expanded.obs,
        selected = selected.obs;
}

class ResetMkNguoiDungController extends GetxController {
  // Observable variables
  var selectedUnitId = 0.obs;
  var selectedUnitName = ''.obs;
  var staffList = <Map<String, dynamic>>[].obs;
  var isResetting = false.obs;
  var resettingStaffId = 0.obs;
  var showUnitTreeDialog = false.obs;

  // Cây đơn vị
  late Rx<UnitTreeNode> unitTree;

  @override
  void onInit() {
    super.onInit();
    _initializeUnitTree();
  }

  void _initializeUnitTree() {
    // Tạo cây đơn vị với cấu trúc cha con
    final rootUnit = UnitTreeNode(
      id: 0,
      name: 'VNPT Cà Mau',
      children: [
        UnitTreeNode(
          id: 1,
          name: 'Ban Giám đốc',
          parentId: 0,
          children: [
            UnitTreeNode(id: 11, name: 'Giám đốc', parentId: 1),
            UnitTreeNode(id: 12, name: 'Phó Giám đốc', parentId: 1),
          ],
        ),
        UnitTreeNode(
          id: 2,
          name: 'Phòng Công nghệ thông tin',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 21, name: 'Bộ phận Phát triển phần mềm', parentId: 2),
            UnitTreeNode(id: 22, name: 'Bộ phận Hạ tầng mạng', parentId: 2),
            UnitTreeNode(
                id: 23, name: 'Bộ phận Bảo mật thông tin', parentId: 2),
          ],
        ),
        UnitTreeNode(
          id: 3,
          name: 'Phòng Hành chính - Nhân sự',
          parentId: 0,
          children: [
            UnitTreeNode(id: 31, name: 'Bộ phận Nhân sự', parentId: 3),
            UnitTreeNode(id: 32, name: 'Bộ phận Hành chính', parentId: 3),
          ],
        ),
        UnitTreeNode(
          id: 4,
          name: 'Phòng Tài chính - Kế toán',
          parentId: 0,
          children: [
            UnitTreeNode(id: 41, name: 'Bộ phận Kế toán', parentId: 4),
            UnitTreeNode(id: 42, name: 'Bộ phận Tài chính', parentId: 4),
          ],
        ),
        UnitTreeNode(
          id: 5,
          name: 'Phòng Kinh doanh',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 51, name: 'Bộ phận Kinh doanh Doanh nghiệp', parentId: 5),
            UnitTreeNode(
                id: 52, name: 'Bộ phận Kinh doanh Cá nhân', parentId: 5),
          ],
        ),
        UnitTreeNode(
          id: 6,
          name: 'Phòng Kỹ thuật',
          parentId: 0,
          children: [
            UnitTreeNode(id: 61, name: 'Bộ phận Vận hành', parentId: 6),
            UnitTreeNode(id: 62, name: 'Bộ phận Bảo trì', parentId: 6),
          ],
        ),
      ],
    );

    unitTree = rootUnit.obs;
  }

  // Methods for unit tree
  void toggleUnitExpansion(UnitTreeNode node) {
    node.expanded.value = !node.expanded.value;
  }

  void selectUnit(int unitId, String unitName) {
    // Clear previous selection
    _clearAllSelections(unitTree.value);

    // Set new selection
    selectedUnitId.value = unitId;
    selectedUnitName.value = unitName;

    // Update staff list
    if (unitId > 0 && staffData.containsKey(unitId)) {
      staffList.value = staffData[unitId]!;
    } else {
      staffList.clear();
    }

    // Mark selected node
    _markSelectedNode(unitTree.value, unitId);

    // Close dialog
    showUnitTreeDialog.value = false;
  }

  void _clearAllSelections(UnitTreeNode node) {
    node.selected.value = false;
    for (var child in node.children) {
      _clearAllSelections(child);
    }
  }

  void _markSelectedNode(UnitTreeNode node, int selectedId) {
    if (node.id == selectedId) {
      node.selected.value = true;
    }
    for (var child in node.children) {
      _markSelectedNode(child, selectedId);
    }
  }

  void showUnitSelector() {
    showUnitTreeDialog.value = true;
  }

  void hideUnitSelector() {
    showUnitTreeDialog.value = false;
  }

// Dữ liệu cán bộ theo đơn vị (có thể thay bằng API call)
  final Map<int, List<Map<String, dynamic>>> staffData = {
    1: [
      {
        'id': 1,
        'name': 'Nguyễn Văn An',
        'position': 'Trưởng phòng',
        'code': 'IT001'
      },
      {
        'id': 2,
        'name': 'Trần Thị Bình',
        'position': 'Chuyên viên',
        'code': 'IT002'
      },
      {
        'id': 3,
        'name': 'Lê Văn Cường',
        'position': 'Nhân viên',
        'code': 'IT003'
      },
    ],
    2: [
      {
        'id': 4,
        'name': 'Phạm Thị Dung',
        'position': 'Trưởng phòng',
        'code': 'HR001'
      },
      {
        'id': 5,
        'name': 'Vũ Văn Em',
        'position': 'Chuyên viên',
        'code': 'HR002'
      },
    ],
    3: [
      {
        'id': 6,
        'name': 'Hoàng Thị Giang',
        'position': 'Trưởng phòng',
        'code': 'FN001'
      },
      {
        'id': 7,
        'name': 'Đỗ Văn Hùng',
        'position': 'Kế toán viên',
        'code': 'FN002'
      },
    ],
    4: [
      {
        'id': 8,
        'name': 'Bùi Thị Lan',
        'position': 'Trưởng phòng',
        'code': 'BD001'
      },
      {
        'id': 9,
        'name': 'Ngô Văn Minh',
        'position': 'Nhân viên kinh doanh',
        'code': 'BD002'
      },
    ],
    5: [
      {
        'id': 10,
        'name': 'Tạ Thị Nga',
        'position': 'Trưởng phòng',
        'code': 'TC001'
      },
      {'id': 11, 'name': 'Lý Văn Ơn', 'position': 'Kỹ sư', 'code': 'TC002'},
    ],
  };

// Methods

  Future<void> resetPassword(Map<String, dynamic> staff) async {
    try {
      isResetting.value = true;
      resettingStaffId.value = staff['id'];

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Thực hiện reset password ở đây
      // await apiService.resetPassword(staff['id']);

      Get.snackbar(
        'Thành công',
        'Đã reset mật khẩu thành công cho ${staff['name']}!',
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể reset mật khẩu cho ${staff['name']}. Vui lòng thử lại!',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isResetting.value = false;
      resettingStaffId.value = 0;
    }
  }
}
