import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/jsTree_model.dart';
import 'package:vnpt_ioffice_camau/app/provider/home/<USER>';

class ResetMkNguoiDungController extends GetxController {
  // Observable variables
  var selectedUnitId = 0.obs;
  var selectedUnitName = ''.obs;
  var staffList = <Map<String, dynamic>>[].obs;
  var isResetting = false.obs;
  var resettingStaffId = 0.obs;
  var showUnitTreeDialog = false.obs;
  var searchQuery = ''.obs;

  // Cây đơn vị
  late Rx<UnitTreeNode> unitTree;
  late Rx<UnitTreeNode> filteredUnitTree;

  @override
  void onInit() {
    super.onInit();
    loadUnitsFromApi();
  }

  void _initializeUnitTree() {
    // Tạo cây đơn vị với cấu trúc cha con
    final rootUnit = UnitTreeNode(
      id: 0,
      name: 'VNPT <PERSON> Mau',
      children: [
        UnitTreeNode(
          id: 1,
          name: '<PERSON>',
          parentId: 0,
          children: [
            UnitTreeNode(id: 11, name: 'Giám đốc', parentId: 1),
            UnitTreeNode(id: 12, name: 'Phó Giám đốc', parentId: 1),
          ],
        ),
        UnitTreeNode(
          id: 2,
          name: 'Phòng Công nghệ thông tin',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 21, name: 'Bộ phận Phát triển phần mềm', parentId: 2),
            UnitTreeNode(id: 22, name: 'Bộ phận Hạ tầng mạng', parentId: 2),
            UnitTreeNode(
                id: 23, name: 'Bộ phận Bảo mật thông tin', parentId: 2),
          ],
        ),
        UnitTreeNode(
          id: 3,
          name: 'Phòng Hành chính - Nhân sự',
          parentId: 0,
          children: [
            UnitTreeNode(id: 31, name: 'Bộ phận Nhân sự', parentId: 3),
            UnitTreeNode(id: 32, name: 'Bộ phận Hành chính', parentId: 3),
          ],
        ),
        UnitTreeNode(
          id: 4,
          name: 'Phòng Tài chính - Kế toán',
          parentId: 0,
          children: [
            UnitTreeNode(id: 41, name: 'Bộ phận Kế toán', parentId: 4),
            UnitTreeNode(id: 42, name: 'Bộ phận Tài chính', parentId: 4),
          ],
        ),
        UnitTreeNode(
          id: 5,
          name: 'Phòng Kinh doanh',
          parentId: 0,
          children: [
            UnitTreeNode(
                id: 51, name: 'Bộ phận Kinh doanh Doanh nghiệp', parentId: 5),
            UnitTreeNode(
                id: 52, name: 'Bộ phận Kinh doanh Cá nhân', parentId: 5),
          ],
        ),
        UnitTreeNode(
          id: 6,
          name: 'Phòng Kỹ thuật',
          parentId: 0,
          children: [
            UnitTreeNode(id: 61, name: 'Bộ phận Vận hành', parentId: 6),
            UnitTreeNode(id: 62, name: 'Bộ phận Bảo trì', parentId: 6),
          ],
        ),
      ],
    );

    unitTree = rootUnit.obs;
    filteredUnitTree = rootUnit.obs;
  }

  // Methods for unit tree
  void toggleUnitExpansion(UnitTreeNode node) {
    node.expanded.value = !node.expanded.value;
  }

  void selectUnit(int unitId, String unitName) {
    // Clear previous selection
    _clearAllSelections(unitTree.value);

    // Set new selection
    selectedUnitId.value = unitId;
    selectedUnitName.value = unitName;

    // Update staff list
    if (unitId > 0 && staffData.containsKey(unitId)) {
      staffList.value = staffData[unitId]!;
    } else {
      staffList.clear();
    }

    // Mark selected node
    _markSelectedNode(unitTree.value, unitId);

    // Close dialog
    showUnitTreeDialog.value = false;
  }

  void _clearAllSelections(UnitTreeNode node) {
    node.selected.value = false;
    for (var child in node.children) {
      _clearAllSelections(child);
    }
  }

  void _markSelectedNode(UnitTreeNode node, int selectedId) {
    if (node.id == selectedId) {
      node.selected.value = true;
    }
    for (var child in node.children) {
      _markSelectedNode(child, selectedId);
    }
  }

  void showUnitSelector() {
    showUnitTreeDialog.value = true;
  }

  void hideUnitSelector() {
    showUnitTreeDialog.value = false;
    searchQuery.value = '';
    _resetFilter();
  }

  void searchUnits(String query) {
    searchQuery.value = query.toLowerCase();
    if (query.isEmpty) {
      _resetFilter();
    } else {
      _filterUnits();
    }
  }

  void _resetFilter() {
    filteredUnitTree.value = _copyUnitTree(unitTree.value);
    _expandAllNodes(filteredUnitTree.value);
  }

  void _filterUnits() {
    filteredUnitTree.value = _filterUnitTree(unitTree.value, searchQuery.value);
    _expandAllNodes(filteredUnitTree.value);
  }

  UnitTreeNode _copyUnitTree(UnitTreeNode original) {
    return UnitTreeNode(
      id: original.id,
      name: original.name,
      parentId: original.parentId,
      expanded: true,
      selected: original.selected.value,
      children: original.children.map((child) => _copyUnitTree(child)).toList(),
    );
  }

  UnitTreeNode _filterUnitTree(UnitTreeNode node, String query) {
    List<UnitTreeNode> filteredChildren = [];

    // Lọc các node con
    for (var child in node.children) {
      var filteredChild = _filterUnitTree(child, query);
      if (_shouldIncludeNode(filteredChild, query)) {
        filteredChildren.add(filteredChild);
      }
    }

    return UnitTreeNode(
      id: node.id,
      name: node.name,
      parentId: node.parentId,
      expanded: true,
      selected: node.selected.value,
      children: filteredChildren,
    );
  }

  bool _shouldIncludeNode(UnitTreeNode node, String query) {
    // Bao gồm node nếu tên chứa từ khóa tìm kiếm
    if (node.name.toLowerCase().contains(query)) {
      return true;
    }

    // Hoặc nếu có node con nào đó thỏa mãn điều kiện
    return node.children.isNotEmpty;
  }

  void _expandAllNodes(UnitTreeNode node) {
    node.expanded.value = true;
    for (var child in node.children) {
      _expandAllNodes(child);
    }
  }

  // Method để load dữ liệu từ API
  Future<void> loadUnitsFromApi() async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Ví dụ dữ liệu JSON từ API
      final apiResponse = await HomeProvider().jsTreeDonViChuQuan(1);
      // Chuyển đổi dữ liệu API thành cây đơn vị
      if (apiResponse['success'] == true && apiResponse['data'] != null) {
        final flatList = apiResponse['data'] as List;
        final rootNode = _buildTreeFromFlatList(flatList);

        unitTree.value = rootNode;
        filteredUnitTree.value = _copyUnitTree(rootNode);
      }
    } catch (e) {
      print('Error loading units from API: $e');
      // Fallback to default data
      _initializeUnitTree();
    }
  }

  // Method để chuyển đổi danh sách phẳng thành cây
  UnitTreeNode _buildTreeFromFlatList(List<dynamic> flatList) {
    Map<String, UnitTreeNode> nodeMap = {};
    UnitTreeNode? rootNode;

    // Tạo tất cả các node trước
    for (var item in flatList) {
      final node = UnitTreeNode.fromJsTree(item);
      nodeMap[node.id.toString()] = node;

      if (item['parent'] == '#') {
        rootNode = node;
      }
    }

    // Xây dựng cấu trúc cây
    for (var item in flatList) {
      final nodeId = item['id'].toString();
      final parentId = item['parent'].toString();

      if (parentId != '#' && nodeMap.containsKey(parentId)) {
        final parentNode = nodeMap[parentId]!;
        final childNode = nodeMap[nodeId]!;

        // Tạo node cha mới với children được cập nhật
        final updatedChildren = List<UnitTreeNode>.from(parentNode.children)
          ..add(childNode);
        final updatedParent = parentNode.copyWith(children: updatedChildren);
        nodeMap[parentId] = updatedParent;
      }
    }

    return rootNode ?? _createDefaultRootNode();
  }

  UnitTreeNode _createDefaultRootNode() {
    return UnitTreeNode(
      id: 0,
      name: 'VNPT Cà Mau',
      expanded: true,
    );
  }

  // Method để gửi dữ liệu lên API
  Future<bool> saveUnitToApi(UnitTreeNode unit) async {
    try {
      // Chuyển đổi unit thành JSON để gửi lên API
      final jsonData = unit.toJson();

      print('Sending to API: $jsonData');

      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Simulate success response
      return true;
    } catch (e) {
      print('Error saving unit to API: $e');
      return false;
    }
  }

// Dữ liệu cán bộ theo đơn vị (có thể thay bằng API call)
  final Map<int, List<Map<String, dynamic>>> staffData = {
    1: [
      {
        'id': 1,
        'name': 'Nguyễn Văn An',
        'position': 'Trưởng phòng',
        'code': 'IT001'
      },
      {
        'id': 2,
        'name': 'Trần Thị Bình',
        'position': 'Chuyên viên',
        'code': 'IT002'
      },
      {
        'id': 3,
        'name': 'Lê Văn Cường',
        'position': 'Nhân viên',
        'code': 'IT003'
      },
    ],
    2: [
      {
        'id': 4,
        'name': 'Phạm Thị Dung',
        'position': 'Trưởng phòng',
        'code': 'HR001'
      },
      {
        'id': 5,
        'name': 'Vũ Văn Em',
        'position': 'Chuyên viên',
        'code': 'HR002'
      },
    ],
    3: [
      {
        'id': 6,
        'name': 'Hoàng Thị Giang',
        'position': 'Trưởng phòng',
        'code': 'FN001'
      },
      {
        'id': 7,
        'name': 'Đỗ Văn Hùng',
        'position': 'Kế toán viên',
        'code': 'FN002'
      },
    ],
    4: [
      {
        'id': 8,
        'name': 'Bùi Thị Lan',
        'position': 'Trưởng phòng',
        'code': 'BD001'
      },
      {
        'id': 9,
        'name': 'Ngô Văn Minh',
        'position': 'Nhân viên kinh doanh',
        'code': 'BD002'
      },
    ],
    5: [
      {
        'id': 10,
        'name': 'Tạ Thị Nga',
        'position': 'Trưởng phòng',
        'code': 'TC001'
      },
      {'id': 11, 'name': 'Lý Văn Ơn', 'position': 'Kỹ sư', 'code': 'TC002'},
    ],
  };

// Methods

  Future<void> resetPassword(Map<String, dynamic> staff) async {
    try {
      isResetting.value = true;
      resettingStaffId.value = staff['id'];

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Thực hiện reset password ở đây
      // await apiService.resetPassword(staff['id']);

      Get.snackbar(
        'Thành công',
        'Đã reset mật khẩu thành công cho ${staff['name']}!',
        backgroundColor: Colors.green[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.check_circle, color: Colors.white),
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể reset mật khẩu cho ${staff['name']}. Vui lòng thử lại!',
        backgroundColor: Colors.red[600],
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: Colors.white),
      );
    } finally {
      isResetting.value = false;
      resettingStaffId.value = 0;
    }
  }
}
