import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/chitiet_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/kyso_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/view_ca_vbdi.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class DetailItemVbdi extends GetView<ChiTietVbdiController> {
  final SetupController setupController = Get.find();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Obx(
      () => (controller.isLoadData.value == true)
          ? Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: WillPopScope(
                  child: Center(
                    child: SpinKitFadingCircle(
                        color: Get.isDarkMode
                            ? AppColor.yellowColor
                            : AppColor.blueAccentColor,
                        size: 40),
                  ),
                  onWillPop: () => Future.value(false)),
            )
          : RefreshIndicator(
              onRefresh: controller.loadChiTietVbdi,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Card(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: "Trích yếu: ",
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                  text: (controller.item.value.data == null)
                                      ? ""
                                      : controller.item.value.data?.trichYeu,
                                  style: const TextStyle(
                                      fontWeight:
                                          FontWeight.bold), // Phong cách in đậm
                                )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Người ký văn bản: ',
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      controller.item.value.data?.nguoiKy ?? "",
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.red), // Phong cách in đậm
                                )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Loại văn bản: ',
                              style: DefaultTextStyle.of(context)
                                  .style, // Sử dụng phong cách mặc định
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller
                                        .item.value.data?.tenLoaiVanBan
                                    // Phong cách in đậm
                                    )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Số bản phát hành: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                  text: controller
                                          .item.value.data?.soBanPhatHanh ??
                                      "",
                                )
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'CQBH: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller.item.value.data
                                            ?.tenCoQuanBanHanh ??
                                        "",
                                    style: const TextStyle(
                                        color: AppColor.helpBlue))
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Ngày lưu: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller.item.value.data?.ngayLuu ==
                                            null
                                        ? ""
                                        : DateFormat('dd/MM/yyyy').format(
                                            controller
                                                .item.value.data!.ngayLuu!))
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Định danh: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: controller
                                        .item.value.data?.maDinhDanhVb)
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: RichText(
                            text: TextSpan(
                              text: 'Vai trò: ',
                              style: DefaultTextStyle.of(context).style,
                              children: <TextSpan>[
                                TextSpan(
                                    text: (controller.item.value.data?.maYeuCau!
                                                .toInt() ==
                                            1
                                        ? ("Phối hợp xử lý")
                                        : (controller.item.value.data?.maYeuCau!
                                                    .toInt() ==
                                                2)
                                            ? ("Xử lý chính")
                                            : ("Xem để biết")),
                                    style: const TextStyle(
                                        color: AppColor.helpBlue))
                              ],
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: Row(
                            children: [
                              Text(
                                "Tệp tin đính kèm: ",
                                style: TextStyle(color: Colors.grey[800]),
                              ),
                              Visibility(
                                visible: controller.indexTab == 0,
                                child: Expanded(
                                  child: ListTile(
                                    leading: const Icon(
                                      Icons.attachment_outlined,
                                      color: Colors.blueAccent,
                                    ),
                                    title: const Text(
                                      "Chọn văn bản đính kèm",
                                      style: TextStyle(
                                          color: Colors.blueAccent,
                                          fontSize: 14),
                                    ),
                                    onTap: () async {
                                      await Get.defaultDialog(
                                          title: "Chọn văn bản đính kèm",
                                          titleStyle:
                                              const TextStyle(fontSize: 14),
                                          content: Column(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  controller.pickFile();
                                                },
                                                child: SvgPicture.asset(
                                                  'svg/upload.svg',
                                                  height: 80,
                                                  width: 80,
                                                ),
                                              ),
                                              if (controller
                                                      .selectedFile.value !=
                                                  null)
                                                Obx(() => Text(controller
                                                    .selectedFileName.value!)),
                                            ],
                                          ),
                                          actions: [
                                            ElevatedButton(
                                              style: ButtonStyle(
                                                  backgroundColor:
                                                      MaterialStateProperty.all<
                                                          Color>(Colors.red)),
                                              onPressed: () {
                                                controller.selectedFile.value =
                                                    File("");
                                                controller.selectedFileName
                                                    .value = '';
                                                Get.back();
                                              },
                                              child: const Text(
                                                "Đóng",
                                                style: TextStyle(
                                                    color: Colors.white),
                                              ),
                                            ),
                                            ElevatedButton(
                                              style: ButtonStyle(
                                                  backgroundColor:
                                                      MaterialStateProperty.all<
                                                          Color>(Colors.blue)),
                                              onPressed: () {
                                                controller.upLoadFileDinhKem();
                                                // Close the dialog without uploading.
                                              },
                                              child: const Text(
                                                "Xác nhận",
                                                style: TextStyle(
                                                    color: Colors.white),
                                              ),
                                            ),
                                          ]);
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        Container(
                          child: Obx(
                            () => ListView(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                children: List.generate(
                                    (controller.item.value.data?.fileVanBan ==
                                            null)
                                        ? 0
                                        : MethodUntils.getFileChiTietTTDH(
                                                controller.item.value.data!
                                                    .fileVanBan!
                                                    .split(":"))
                                            .length, (index) {
                                  return ListTile(
                                    leading: Icon(
                                      MethodUntils.getFileChiTietTTDH(controller
                                              .item.value!.data!.fileVanBan!
                                              .split(":"))[index]
                                          .iconFile,
                                      color: MethodUntils.getFileChiTietTTDH(
                                              controller
                                                  .item.value!.data!.fileVanBan!
                                                  .split(":"))[index]
                                          .colorIcon,
                                    ),
                                    title: GestureDetector(
                                      onTap: () {
                                        ModalViewFileOnline.ViewFileOnline(
                                            tenFile:
                                                MethodUntils.getFileChiTietTTDH(
                                                        controller.item.value!
                                                            .data!.fileVanBan!
                                                            .split(":"))[index]
                                                    .fileName!,
                                            item: controller
                                                .item.value!.data!.fileVanBan!
                                                .split(":")[index],
                                            path: MethodUntils.getFileChiTietTTDH(
                                                    controller.item.value!.data!
                                                        .fileVanBan!
                                                        .split(":"))[index]
                                                .urlViewFile!);
                                        // setupController.openFile(
                                        //     url:
                                        //         MethodUntils.getFileChiTietTTDH(
                                        //                 controller.item.value!
                                        //                     .data!.fileVanBan!
                                        //                     .split(":"))[index]
                                        //             .urlViewFile!,
                                        //     fileName:
                                        //         MethodUntils.getFileChiTietTTDH(
                                        //                 controller.item.value!
                                        //                     .data!.fileVanBan!
                                        //                     .split(":"))[index]
                                        //             .fileName!,
                                        //     indexFile: index);
                                      },
                                      child: Text(
                                        MethodUntils.getFileName(controller
                                            .item.value!.data!.fileVanBan!
                                            .split(":")[index]
                                            .toString()),
                                        style: const TextStyle(
                                            color: AppColor.blackColor,
                                            fontSize: 13),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Visibility(
                                          visible: (controller.indexTab == 0),
                                          child: GestureDetector(
                                              onTap: () {
                                                controller.onChuyenPageKySo(
                                                    controller.item.value.data!
                                                        .fileVanBan!
                                                        .split(":")[index]
                                                        .toString(),
                                                    0);
                                              },
                                              child: const Icon(Icons.edit,
                                                  color: Colors.blue)),
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 8.0),
                                          child: Visibility(
                                            visible: (controller.indexTab == 0),
                                            child: GestureDetector(
                                                onTap: () {
                                                  controller.onChuyenPageKySo(
                                                      controller.item.value
                                                          .data!.fileVanBan!
                                                          .split(":")[index]
                                                          .toString(),
                                                      1);
                                                },
                                                child:
                                                    const Icon(Icons.storage)),
                                          ),
                                        ),
                                        Visibility(
                                          visible: controller.indexTab == 0,
                                          child: GestureDetector(
                                            onTap: () {
                                              controller.confirmDeleteFile(
                                                  controller.item.value!.data!
                                                      .fileVanBan!
                                                      .split(":")[index],
                                                  MethodUntils.getFileChiTietTTDH(
                                                          controller.item.value!
                                                              .data!.fileVanBan!
                                                              .split(
                                                                  ":"))[index]
                                                      .fileName!);
                                            },
                                            child: const Icon(
                                              Icons.delete_forever,
                                              color: Colors.red,
                                              size: 25,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                })),
                          ),
                        ),
                        // phiêú trình
                        Padding(
                          padding: const EdgeInsets.only(left: 15, top: 10),
                          child: Row(
                            children: [
                              Text(
                                "File phiếu trình: ",
                                style: TextStyle(color: Colors.grey[800]),
                              ),
                              Visibility(
                                visible: controller.indexTab == 0,
                                child: Expanded(
                                  child: ListTile(
                                    leading: const Icon(
                                      Icons.attachment_outlined,
                                      color: Colors.blueAccent,
                                    ),
                                    title: const Text(
                                      "Chọn văn bản đính kèm",
                                      style: TextStyle(
                                          color: Colors.blueAccent,
                                          fontSize: 14),
                                    ),
                                    onTap: () async {
                                      await Get.defaultDialog(
                                          title: "Chọn văn bản đính kèm",
                                          titleStyle:
                                              const TextStyle(fontSize: 14),
                                          content: Column(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  controller
                                                      .pickFilePhieuTrinh();
                                                },
                                                child: SvgPicture.asset(
                                                  'svg/upload.svg',
                                                  height: 80,
                                                  width: 80,
                                                ),
                                              ),
                                              if (controller
                                                      .selectedFileNamePhieuTrinh
                                                      .value !=
                                                  null)
                                                Obx(() => Text(controller
                                                    .selectedFileNamePhieuTrinh
                                                    .value!)),
                                            ],
                                          ),
                                          actions: [
                                            ElevatedButton(
                                              style: ButtonStyle(
                                                  backgroundColor:
                                                      MaterialStateProperty.all<
                                                          Color>(Colors.red)),
                                              onPressed: () {
                                                controller
                                                    .selectedFilePhieuTrinh
                                                    .value = File("");
                                                controller
                                                    .selectedFileNamePhieuTrinh
                                                    .value = '';
                                                Get.back();
                                              },
                                              child: const Text(
                                                "Đóng",
                                                style: TextStyle(
                                                    color: Colors.white),
                                              ),
                                            ),
                                            ElevatedButton(
                                              style: ButtonStyle(
                                                  backgroundColor:
                                                      MaterialStateProperty.all<
                                                          Color>(Colors.blue)),
                                              onPressed: () {
                                                // controller.upLoadFileDinhKem();
                                                // Close the dialog without uploading.
                                                controller
                                                    .uploadFilePhieuTrinh();
                                              },
                                              child: const Text(
                                                "Xác nhận",
                                                style: TextStyle(
                                                    color: Colors.white),
                                              ),
                                            ),
                                          ]);
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        Visibility(
                          visible:
                              (controller.item.value.data?.fileDinhKem != null),
                          child: Container(
                            child: Obx(
                              () => ListView(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  children: List.generate(
                                      (controller.item.value.data
                                                  ?.fileDinhKem ==
                                              null)
                                          ? 0
                                          : MethodUntils.getFileChiTietTTDH(
                                                  controller.item.value.data!
                                                      .fileDinhKem!
                                                      .split(":"))
                                              .length, (index) {
                                    return ListTile(
                                      leading: Icon(
                                        MethodUntils.getFileChiTietTTDH(
                                                controller.item.value.data!
                                                    .fileDinhKem!
                                                    .split(":"))[index]
                                            .iconFile,
                                        color: MethodUntils.getFileChiTietTTDH(
                                                controller.item.value.data!
                                                    .fileDinhKem!
                                                    .split(":"))[index]
                                            .colorIcon,
                                      ),
                                      title: GestureDetector(
                                        onTap: () {
                                          ModalViewFileOnline.ViewFileOnline(
                                              tenFile: MethodUntils.getFileChiTietTTDH(
                                                      controller.item.value
                                                          .data!.fileDinhKem!
                                                          .split(":"))[index]
                                                  .fileName!,
                                              item: controller
                                                  .item.value.data!.fileDinhKem!
                                                  .split(":")[index],
                                              path:
                                                  MethodUntils.getFileChiTietTTDH(
                                                          controller
                                                              .item
                                                              .value
                                                              .data!
                                                              .fileDinhKem!
                                                              .split(":"))[index]
                                                      .urlViewFile!);
                                        },
                                        child: Text(
                                          MethodUntils.getFileName(controller
                                              .item.value.data!.fileDinhKem!
                                              .split(":")[index]
                                              .toString()),
                                          style: const TextStyle(
                                              color: AppColor.blackColor,
                                              fontSize: 13),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      trailing: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Visibility(
                                            visible: (controller.indexTab == 0),
                                            child: GestureDetector(
                                                onTap: () {
                                                  controller
                                                      .onChuyenPageKsPhieuTrinh(
                                                          controller
                                                              .item
                                                              .value
                                                              .data!
                                                              .fileDinhKem!
                                                              .split(":")[index]
                                                              .toString(),
                                                          0);
                                                },
                                                child: const Icon(Icons.edit,
                                                    color: Colors.blue)),
                                          ),
                                          // Padding(
                                          //   padding:
                                          //       const EdgeInsets.only(left: 8.0),
                                          //   child: Visibility(
                                          //     visible: (controller.indexTab == 0),
                                          //     child: GestureDetector(
                                          //         onTap: () {
                                          //           controller.onChuyenPageKySo(
                                          //               controller.item.value
                                          //                   .data!.fileDinhKem!
                                          //                   .split(":")[index]
                                          //                   .toString(),
                                          //               1);
                                          //         },
                                          //         child:
                                          //             const Icon(Icons.storage)),
                                          //   ),
                                          // ),
                                          // Visibility(
                                          //   visible: controller.indexTab == 0,
                                          //   child: GestureDetector(
                                          //     onTap: () {
                                          //       controller.confirmDeleteFile(
                                          //           controller.item.value!.data!
                                          //               .fileDinhKem!
                                          //               .split(":")[index],
                                          //           MethodUntils.getFileChiTietTTDH(
                                          //                   controller
                                          //                       .item
                                          //                       .value!
                                          //                       .data!
                                          //                       .fileDinhKem!
                                          //                       .split(
                                          //                           ":"))[index]
                                          //               .fileName!);
                                          //     },
                                          //     child: const Icon(
                                          //       Icons.delete_forever,
                                          //       color: Colors.red,
                                          //       size: 25,
                                          //     ),
                                          //   ),
                                          // ),
                                        ],
                                      ),
                                    );
                                  })),
                            ),
                          ),
                        ),

                        Visibility(
                          visible:
                              controller.dsVanBanLienQuan.value!.isNotEmpty,
                          child: const Padding(
                            padding: const EdgeInsets.only(left: 15, top: 10),
                            child: Text("Văn bản liên quan:"),
                          ),
                        ),
                        Container(
                          child: Obx(
                            () => ListView(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                children: List.generate(
                                    controller.dsVanBanLienQuan.value!.isEmpty
                                        ? 0
                                        : MethodUntils.getFileChiTietTTDH(
                                                controller
                                                    .dsVanBanLienQuan.value!
                                                    .split(":"))
                                            .length, (index) {
                                  return ListTile(
                                    leading: Icon(
                                      MethodUntils.getFileChiTietTTDH(controller
                                              .dsVanBanLienQuan.value!
                                              .split(":"))[index]
                                          .iconFile,
                                      color: MethodUntils.getFileChiTietTTDH(
                                              controller.dsVanBanLienQuan.value!
                                                  .split(":"))[index]
                                          .colorIcon,
                                    ),
                                    title: GestureDetector(
                                      onTap: () {
                                        ModalViewFileOnline.ViewFileOnline(
                                            tenFile:
                                                MethodUntils.getFileChiTietTTDH(
                                                        controller
                                                            .dsVanBanLienQuan
                                                            .value!
                                                            .split(":"))[index]
                                                    .fileName!,
                                            item: controller
                                                .dsVanBanLienQuan.value!
                                                .split(":")[index],
                                            path:
                                                MethodUntils.getFileChiTietTTDH(
                                                        controller
                                                            .dsVanBanLienQuan
                                                            .value!
                                                            .split(":"))[index]
                                                    .urlViewFile!);
                                        // setupController.openFile(
                                        //     url:
                                        //         MethodUntils.getFileChiTietTTDH(
                                        //                 controller.item.value!
                                        //                     .data!.fileVanBan!
                                        //                     .split(":"))[index]
                                        //             .urlViewFile!,
                                        //     fileName:
                                        //         MethodUntils.getFileChiTietTTDH(
                                        //                 controller.item.value!
                                        //                     .data!.fileVanBan!
                                        //                     .split(":"))[index]
                                        //             .fileName!,
                                        //     indexFile: index);
                                      },
                                      child: Text(
                                        MethodUntils.getFileName(controller
                                            .dsVanBanLienQuan.value!
                                            .split(":")[index]
                                            .toString()),
                                        style: const TextStyle(
                                            color: AppColor.blackColor,
                                            fontSize: 13),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  );
                                })),
                          ),
                        ),

                        const Padding(
                          padding: EdgeInsets.only(bottom: 10),
                        ),
                        const Divider(
                          height: 2,
                          color: AppColor.greyColor,
                        ),
                        Visibility(
                          visible: (controller.indexTab == 0 &&
                              controller.isLanhDao == 1),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  controller.onSendVanThuNhanh();
                                  // Button onPressed callback
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: const Text(
                                  'Duyệt nhanh',
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.white),
                                ),
                              ),
                              const Padding(padding: EdgeInsets.only(left: 10)),
                              ElevatedButton(
                                onPressed: () {
                                  // Button onPressed callback
                                  controller.onChuyentVt(controller.item.value);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: const Text(
                                  'Duyệt',
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.white),
                                ),
                              ),
                              const Padding(padding: EdgeInsets.only(left: 10)),
                              ElevatedButton(
                                  onPressed: () {
                                    // Button onPressed callback
                                    controller
                                        .onChuyenLdk(controller.item.value);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: const Text(
                                    'Chuyển LĐK',
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.white),
                                  )),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: (controller.indexTab == 0 &&
                              controller.isLanhDao == 1),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Padding(
                                    padding: EdgeInsets.only(left: 10)),
                                ElevatedButton(
                                    onPressed: () {
                                      controller
                                          .onChuyenCv(controller.item.value);
                                      // Button onPressed callback
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                    ),
                                    child: const Text(
                                      'Chuyển CV',
                                      style: TextStyle(
                                          fontSize: 14, color: Colors.white),
                                    )),
                              ]),
                        ),
                        // chuyên viên
                        Visibility(
                          visible: (controller.indexTab == 3 &&
                              controller.isChuyenVien == 1),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  // Button onPressed callback
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: const Text(
                                  'Chuyển tiếp',
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.white),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: (controller.indexTab == 0 &&
                              controller.isChuyenVien == 1),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  controller.onSuccessVbdi();
                                  // Button onPressed callback
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: const Text(
                                  'Hoàn Tất',
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.white),
                                ),
                              ),
                              const Padding(padding: EdgeInsets.only(left: 10)),
                              ElevatedButton(
                                onPressed: () {
                                  // Button onPressed callback
                                  controller
                                      .onCvChuyenLd(controller.item.value);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                child: const Text(
                                  'Gửi LĐ',
                                  style: TextStyle(
                                      fontSize: 14, color: Colors.white),
                                ),
                              ),
                              const Padding(padding: EdgeInsets.only(left: 10)),
                              ElevatedButton(
                                  onPressed: () {
                                    // Button onPressed callback
                                    controller
                                        .onCvChuyenCv(controller.item.value);
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8, horizontal: 16),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                  ),
                                  child: const Text(
                                    'Gửi CV',
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.white),
                                  )),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: (controller.indexTab == 0 &&
                              controller.isChuyenVien == 1),
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Padding(
                                    padding: EdgeInsets.only(left: 10)),
                                ElevatedButton(
                                    onPressed: () {
                                      // Button onPressed callback
                                      controller
                                          .onCvChuyenVt(controller.item.value);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 8, horizontal: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                    ),
                                    child: const Text(
                                      'Gửi VT',
                                      style: TextStyle(
                                          fontSize: 14, color: Colors.white),
                                    )),
                              ]),
                        ),
                      ],
                    )),
                    Visibility(
                      visible: controller.dsQtxlVbdi.isNotEmpty,
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const Text("Tổng hợp ý kiến xử lý",
                                style: TextStyle(
                                    color: AppColor.blackColor,
                                    fontWeight: FontWeight.w600)),
                            const Padding(padding: EdgeInsets.only(bottom: 10)),
                            const Divider(
                              height: 2,
                              color: AppColor.greyColor,
                            ),
                            Obx(
                              () => Column(
                                children: controller.dsQtxlVbdi
                                    .map((qtxl) => Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.all(5),
                                                child: Text(
                                                  "Người gửi: ${qtxl.canBoGui}",
                                                  style: const TextStyle(
                                                      color: AppColor.helpBlue),
                                                ),
                                              ),
                                              Padding(
                                                  padding:
                                                      const EdgeInsets.all(5),
                                                  child: Text(
                                                    "Người nhận: ${qtxl.canBoNhan}",
                                                  )),
                                              Padding(
                                                padding:
                                                    const EdgeInsets.all(5),
                                                child: Text(
                                                    "Ngày nhận: ${DateFormat('dd/MM/yyyy HH:ss').format(qtxl.ngayNhan!)}"),
                                              ),
                                              const Divider(
                                                color: Colors.grey,
                                              )
                                            ]))
                                    .toList(),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    ));
  }
}
