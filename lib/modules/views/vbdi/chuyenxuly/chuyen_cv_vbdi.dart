import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/xulyld_vbdi_controller.dart';

class ChuyenCvVbdi extends GetView<XulyLdVbdiController> {
  ChuyenCvVbdi({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      constraints: BoxConstraints(maxHeight: size.height),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0, top: 10),
          child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            const Text("Ý kiến chuyển:"),
            SizedBox(
              width: (MediaQuery.of(context).size.width - 120),
              child: Text<PERSON><PERSON><PERSON><PERSON>(
                maxLines: 2,
                keyboardType: TextInputType.multiline,
                onSaved: (value) {},
                validator: (value) {
                  if (value!.isEmpty) {
                    return '';
                  }
                  return null;
                },
                controller: controller.inputYKienCv,
                decoration: const InputDecoration(
                  labelText: '',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ]),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 110),
          child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            const Text("sms"),
            Obx(() => Checkbox(
                value: controller.isSmsCv.value,
                onChanged: (value) {
                  controller.isSmsCv.value = value!;
                }))
          ]),
        ),
        const Padding(
          padding: EdgeInsets.all(8.0),
          child: Text("Danh sách chuyên viên:"),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: SizedBox(
            height: 45,
            child: TextFormField(
              cursorColor: Color.fromARGB(255, 242, 237, 237),
              style: const TextStyle(color: AppColor.blackColor),
              decoration: const InputDecoration(
                  errorStyle: TextStyle(color: AppColor.helpBlue),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(width: 1, color: AppColor.helpBlue),
                  ),
                  labelStyle: TextStyle(color: AppColor.helpBlue),
                  focusColor: AppColor.blackColor,
                  prefixIcon: Icon(
                    Icons.search_outlined,
                    color: AppColor.helpBlue,
                    size: 20,
                  ),
                  hintText: "Nhập nội dung tìm kiếm...",
                  hintStyle: TextStyle(fontSize: 13)),
              onChanged: (value) {
                controller.setSearchTextCv(value);
              },
            ),
          ),
        ),
        const Padding(
          padding: EdgeInsets.only(left: 15, right: 15, top: 10),
          child: Divider(
            height: 1,
            color: Colors.blueGrey,
          ),
        ),
        Expanded(
          flex: 1,
          child: SingleChildScrollView(
            child: Container(
                child: Obx(
              () => ListView(
                shrinkWrap: true,
                children: List.generate(
                    controller.fillterDsChuyenVienVbdi.length, (index) {
                  return ListTile(
                    leading: Checkbox(
                        value: controller.checkBoxChuyenVien[controller
                                .fillterDsChuyenVienVbdi[index].maCtcbKc!
                                .toInt()] ??
                            false,
                        onChanged: (newvalue) {
                          controller.toggleCheckboxCvValue(controller
                              .fillterDsChuyenVienVbdi[index].maCtcbKc!
                              .toInt());
                        }),
                    title: Text(controller
                        .fillterDsChuyenVienVbdi[index].hoVaTenCanBo
                        .toString()),
                    subtitle: RichText(
                        text: TextSpan(children: [
                      TextSpan(
                          text:
                              'Chức vụ: ${controller.fillterDsChuyenVienVbdi[index].tenChucVu}',
                          style: const TextStyle(color: AppColor.greyColor)),
                      const TextSpan(text: '\n'),
                      TextSpan(
                          text:
                              'Đơn vị: ${controller.fillterDsChuyenVienVbdi[index].tenDonVi}',
                          style: const TextStyle(color: AppColor.greyColor))
                    ])),
                  );
                }),
              ),
            )),
          ),
        )
      ]),
    );
  }
}
