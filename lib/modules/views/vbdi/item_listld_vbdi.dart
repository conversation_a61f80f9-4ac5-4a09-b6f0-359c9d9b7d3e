import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';

class ItemListLdVbdi extends StatelessWidget {
  final String trichYeu;
  final String coQuanBanHanh;
  final String ngayNhan;
  final String doKhan;
  final String doMat;
  final int? isXem;
  final VoidCallback onClickItem;
  const ItemListLdVbdi(
      {super.key,
      required this.trichYeu,
      required this.do<PERSON>han,
      required this.doMat,
      this.isXem,
      required this.coQuanBanHanh,
      required this.ngayNhan,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: GestureDetector(
        child: Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(width: 0.3))),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                fit: FlexFit.tight,
                child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          (isXem == null)
                              ? Text(trichYeu,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14.0,
                                  ))
                              : Text(
                                  trichYeu,
                                ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "CQBH: $coQuanBanHanh",
                            style: const TextStyle(fontSize: 13.0),
                          ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Độ khẩn: $doKhan",
                            style: const TextStyle(fontSize: 13.0),
                          ),
                          const Padding(
                              padding: EdgeInsets.symmetric(vertical: 2.0)),
                          Text(
                            "Độ mật: $doMat",
                            style: const TextStyle(fontSize: 13.0),
                          )
                        ])),
              ),
              Flexible(
                flex: 1,
                fit: FlexFit.tight,
                child: Container(
                    child: Center(
                  child: Text(ngayNhan, style: const TextStyle(fontSize: 13.0)),
                )),
              ),
            ],
          ),
        ),
        onTap: onClickItem,
      ),
    );
  }
}
