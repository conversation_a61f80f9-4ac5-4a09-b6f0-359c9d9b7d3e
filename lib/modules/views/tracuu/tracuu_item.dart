import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';

class ItemTraCuuVanBan extends StatelessWidget {
  final String trichYeu;
  final String sokyhieu;
  final String ngayBanHanh;
  final String loaiVanBan;
  final String nguoiky;
  final VoidCallback onClickItem;
  const ItemTraCuuVanBan(
      {super.key,
      required this.trichYeu,
      required this.sokyhieu,
      required this.ngayBanHanh,
      required this.loaiVanBan,
      required this.nguoiky,
      required this.onClickItem});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(bottom: BorderSide(width: 0.3))),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(trichYeu,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14.0,
                            )),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Text(sokyhieu,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14.0,
                            )),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Text(
                          "Ngày ban hanh: $ngayBanHanh",
                          style: const TextStyle(fontSize: 13.0),
                        ),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Text(
                          "Loại: $loaiVanBan",
                          style: const TextStyle(fontSize: 13.0),
                        ),
                        const Padding(
                            padding: EdgeInsets.symmetric(vertical: 2.0)),
                        Text(
                          "Người ký: $nguoiky",
                          style: const TextStyle(fontSize: 13.0),
                        ),
                      ])),
            ),
          ],
        ),
      ),
      onTap: onClickItem,
    );
  }
}
