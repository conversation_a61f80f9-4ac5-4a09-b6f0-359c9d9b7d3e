import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/dscv_vbde_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/item_listcv_vbde.dart';

class XulyCvVbde extends GetView<DsCvVbdeController> {
  final HomeController homeController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        Container(
          child: Column(children: [
            TabBar(
                padding: const EdgeInsets.fromLTRB(5, 2, 5, 2),
                labelColor: AppColor.helpBlue,
                controller: controller.tabController,
                onTap: (value) {
                  controller.getDsCvByIndexTab(value);
                },
                tabs: [
                  Obx(() => Tab(
                      icon: (homeController.nvVbdeXuly == 0)
                          ? const Text("Chưa xử lý")
                          : badges.Badge(
                              badgeStyle: const badges.BadgeStyle(
                                badgeColor: Colors.red,
                              ),
                              position: badges.BadgePosition.topEnd(
                                  top: -20, end: -12),
                              badgeContent: Text(
                                homeController.nvVbdeXuly.toString(),
                                style: const TextStyle(color: Colors.white),
                              ),
                              child: const Text("Chưa xử lý"),
                            ))),
                  const Tab(text: "Đang xử lý"),
                  const Tab(text: "Đã Xử lý"),
                ]),
            Padding(
              padding: const EdgeInsets.all(5.0),
              child: SizedBox(
                height: 45,
                child: TextFormField(
                    cursorColor: const Color.fromARGB(255, 242, 237, 237),
                    style: const TextStyle(color: AppColor.blackColor),
                    onFieldSubmitted: (newValue) {
                      controller.setSearchKeyWord(newValue);
                    },
                    decoration: const InputDecoration(
                        errorStyle: TextStyle(color: AppColor.helpBlue),
                        border: OutlineInputBorder(
                          borderSide:
                              BorderSide(width: 1, color: AppColor.helpBlue),
                        ),
                        labelStyle: TextStyle(color: AppColor.helpBlue),
                        focusColor: AppColor.blackColor,
                        prefixIcon: Icon(
                          Icons.search_outlined,
                          color: AppColor.helpBlue,
                          size: 20,
                        ),
                        hintText: "Nhập nội dung tìm kiếm...",
                        hintStyle: TextStyle(fontSize: 13))),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Checkbox(
                          value: controller.istatcads.value,
                          onChanged: (value) {
                            controller.isChangeAllDS(value!);
                          }),
                      const Text("Tất cả"),
                      Checkbox(
                          value: controller.isxlc.value,
                          onChanged: (value) {
                            controller.isChangeXLC(value!);
                          }),
                      const Text("XLC"),
                      Checkbox(
                          value: controller.isph.value,
                          onChanged: (newvalue) {
                            controller.isChangePH(newvalue!);
                          }),
                      const Text("PH"),
                      Checkbox(
                          value: controller.isxdb.value,
                          onChanged: (value) {
                            controller.isChangeXDB(value!);
                          }),
                      const Text("XĐB"),
                    ],
                  )),
            )
          ]),
        ),
        Expanded(
          child: TabBarView(
            controller: controller.tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              Container(
                color: AppColor.whiteColor,
                child: controller.obx(
                    (dsCvChuaXL) => controller.indexTabCvGobal.value != 0
                        ? const Text("")
                        : RefreshIndicator(
                            onRefresh: () async {
                              controller.LoadData();
                            },
                            child: ListView.builder(
                                controller:
                                    controller.scrollerControllerChuaXuLy,
                                shrinkWrap: true,
                                physics: const AlwaysScrollableScrollPhysics(),
                                itemCount: controller.dsChuaXuLy.length,
                                itemBuilder: ((context, index) =>
                                    ItemListCvVbde(
                                      trichYeu:
                                          dsCvChuaXL![index].trichYeu ?? "",
                                      soKyHieu:
                                          dsCvChuaXL[index].soKyHieu ?? "",
                                      isXem: dsCvChuaXL[index].ngayXem,
                                      coQuanBanHanh:
                                          dsCvChuaXL[index].tenCoQuanBanHanh ??
                                              "",
                                      ngayVanBanDen:
                                          dsCvChuaXL[index].ngayDen ?? "",
                                      vaiTro:
                                          dsCvChuaXL[index].maYeuCau!.toInt(),
                                      onClickItem: () {
                                        controller
                                            .onDetailVbde(dsCvChuaXL[index]);
                                      },
                                    ))),
                          ),
                    onLoading: SpinKitCircle(
                      color: AppColor.blueAccentColor,
                    ),
                    onEmpty: Container(
                      child: Center(
                          child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Lottie.asset("lottie/emptyBox.json",
                              height: 200, width: 200),
                          const Text("Không tìm thấy dữ liệu!")
                        ],
                      )),
                    )),
              ),
              Container(
                  color: AppColor.whiteColor,
                  child: controller.obx(
                      (dsCvDangXL) => controller.indexTabCvGobal.value != 1
                          ? const Text("")
                          : ListView.builder(
                              controller: controller.scrollerControllerDangXuLy,
                              physics: const AlwaysScrollableScrollPhysics(),
                              itemCount: controller.dsDangXuLy.length,
                              itemBuilder: ((context, index) => ItemListCvVbde(
                                    trichYeu: dsCvDangXL![index].trichYeu ?? "",
                                    soKyHieu: dsCvDangXL[index].soKyHieu ?? "",
                                    isXem: dsCvDangXL[index].ngayXem,
                                    coQuanBanHanh:
                                        dsCvDangXL[index].tenCoQuanBanHanh ??
                                            "",
                                    ngayVanBanDen:
                                        dsCvDangXL[index].ngayDen ?? "",
                                    vaiTro: dsCvDangXL[index].maYeuCau!.toInt(),
                                    onClickItem: () {
                                      controller
                                          .onDetailVbde(dsCvDangXL[index]);
                                    },
                                  ))),
                      onLoading: SpinKitCircle(
                        color: AppColor.blueAccentColor,
                      ),
                      onEmpty: Container(
                        child: Center(
                            child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Lottie.asset("lottie/emptyBox.json",
                                height: 200, width: 200),
                            const Text("Không tìm thấy dữ liệu!")
                          ],
                        )),
                      ))),
              Container(
                  color: AppColor.whiteColor,
                  child: controller.obx(
                      (dsCvDaXL) => controller.indexTabCvGobal.value != 2
                          ? const Text("")
                          : RefreshIndicator(
                              onRefresh: () async {
                                controller.LoadData();
                              },
                              child: ListView.builder(
                                  controller:
                                      controller.scrollerControllerDaXuLy,
                                  physics:
                                      const AlwaysScrollableScrollPhysics(),
                                  itemCount: controller.dsDaXuly.length,
                                  itemBuilder: ((context, index) =>
                                      ItemListCvVbde(
                                        trichYeu:
                                            dsCvDaXL![index].trichYeu ?? "",
                                        soKyHieu:
                                            dsCvDaXL[index].soKyHieu ?? "",
                                        isXem: dsCvDaXL[index].ngayXem,
                                        coQuanBanHanh:
                                            dsCvDaXL[index].tenCoQuanBanHanh ??
                                                "",
                                        ngayVanBanDen:
                                            dsCvDaXL[index].ngayDen ?? "",
                                        vaiTro:
                                            dsCvDaXL[index].maYeuCau!.toInt(),
                                        onClickItem: () {
                                          controller
                                              .onDetailVbde(dsCvDaXL[index]);
                                        },
                                      ))),
                            ),
                      onLoading: SpinKitCircle(
                        color: AppColor.blueAccentColor,
                      ),
                      onEmpty: Container(
                        child: Center(
                            child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Lottie.asset("lottie/emptyBox.json",
                                height: 200, width: 200),
                            const Text("Không tìm thấy dữ liệu!")
                          ],
                        )),
                      ))),
            ],
          ),
        ),
      ],
    ));
  }
}
