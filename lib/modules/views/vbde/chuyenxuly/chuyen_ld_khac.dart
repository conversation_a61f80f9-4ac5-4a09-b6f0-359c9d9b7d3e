import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/xulyld_vbde_controller.dart';

class ChuyenLanhDaoKhac extends GetView<XuLyLdVbdeController> {
  ChuyenLanhDaoKhac({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      constraints: BoxConstraints(maxHeight: size.height),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: const EdgeInsets.only(left: 8.0, top: 10),
          child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            const Text("Ý kiến chuyển:"),
            SizedBox(
              width: (MediaQuery.of(context).size.width - 120),
              child: TextFormField(
                maxLines: 2,
                keyboardType: TextInputType.multiline,
                controller: controller.yKienLdkInputController,
                onSaved: (value) {
                  controller.yKienLdkInputController.text = value!;
                },
                validator: (value) {
                  if (value!.isEmpty) {
                    return '';
                  }
                  return null;
                },
                decoration: const InputDecoration(
                  labelText: '',
                  border: OutlineInputBorder(),
                ),
              ),
            ),
          ]),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 110),
          child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            const Text("sms"),
            Obx(() => Checkbox(
                value: controller.isSmsLdk.value,
                onChanged: (value) {
                  controller.changeSmsLdk(value!);
                }))
          ]),
        ),
        const Padding(
          padding: EdgeInsets.all(8.0),
          child: Text("Danh sách lãnh đạo:"),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 15, right: 15),
          child: SizedBox(
            height: 45,
            child: TextFormField(
              cursorColor: Color.fromARGB(255, 242, 237, 237),
              style: const TextStyle(color: AppColor.blackColor),
              decoration: const InputDecoration(
                  errorStyle: TextStyle(color: AppColor.helpBlue),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(width: 1, color: AppColor.helpBlue),
                  ),
                  labelStyle: TextStyle(color: AppColor.helpBlue),
                  focusColor: AppColor.blackColor,
                  prefixIcon: Icon(
                    Icons.search_outlined,
                    color: AppColor.helpBlue,
                    size: 20,
                  ),
                  hintText: "Nhập nội dung tìm kiếm...",
                  hintStyle: TextStyle(fontSize: 13)),
              onChanged: (value) {
                controller.setSearchText(value);
              },
            ),
          ),
        ),
        const Padding(
          padding: EdgeInsets.only(left: 15, right: 15, top: 10),
          child: Divider(
            height: 1,
            color: Colors.blueGrey,
          ),
        ),
        Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Container(
                child: Obx(() => ListView(
                      shrinkWrap: true,
                      children: List.generate(
                          controller.fillterDsLanhDaoKhac.length, (index) {
                        return ListTile(
                          leading: Checkbox(
                              value: controller.checkBoxLanhDaoKhac[controller
                                      .fillterDsLanhDaoKhac[index].ma!
                                      .toInt()] ??
                                  false,
                              onChanged: (value) {
                                controller.toggleCheckboxValue(controller
                                    .fillterDsLanhDaoKhac[index].ma!
                                    .toInt());
                              }),
                          title: Text(controller.fillterDsLanhDaoKhac[index].ten
                              .toString()),
                          subtitle: RichText(
                              text: TextSpan(children: [
                            TextSpan(
                                text:
                                    'chức vụ: ${controller.fillterDsLanhDaoKhac[index].tenChucVu}',
                                style:
                                    const TextStyle(color: AppColor.greyColor)),
                            const TextSpan(text: '\n'),
                            TextSpan(
                                text:
                                    'Đơn vị: ${controller.fillterDsLanhDaoKhac[index].tenDonVi}',
                                style:
                                    const TextStyle(color: AppColor.greyColor))
                          ])),
                        );
                      }),
                    )),
              ),
            ))
      ]),
    );
  }
}
