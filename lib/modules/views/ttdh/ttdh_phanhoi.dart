import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_xuly_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/ttdh/ttdh_tree_canbo.dart';
import 'package:vnpt_ioffice_camau/modules/views/ttdh/ttdh_tree_nhomdvn.dart';

class TTDHPhanhoi extends GetView<TtdhXuLyController> {
  SetupController setupController = Get.find();
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        decoration: const BoxDecoration(color: Colors.white),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Padding(
            padding: EdgeInsets.only(top: 20, left: 10),
            child: Text(
              "Loại thông tin",
              style: TextStyle(color: Colors.blue),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 5, left: 10, right: 10),
            child: TextFormField(
              controller: controller.loaiThongDiep,
              onTap: () {
                showModalBottomSheet(
                  shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10),
                          topRight: Radius.circular(10))),
                  context: context,
                  builder: (BuildContext context) {
                    return Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.all(5),
                            child: Text("Chọn loại thông tin điều hành",
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall!
                                    .copyWith(
                                      color: AppColor.greyColor,
                                    )),
                          ),
                          const Divider(color: AppColor.helpBlue),
                          Obx(() => Column(
                                children: controller.dsLoaiThongDiep!.value
                                    .map(
                                      (element) => SingleChildScrollView(
                                        child: Column(
                                          children: [
                                            ListTile(
                                              title: Center(
                                                child: Text(
                                                    element.tenLoaiTtdh ?? "",
                                                    style: const TextStyle(
                                                        color:
                                                            AppColor.helpBlue)),
                                              ),
                                              onTap: () {
                                                controller
                                                    .changeSelectLoaiThongDiep(
                                                        element);
                                              },
                                            ),
                                            const Divider(
                                                color: AppColor.helpBlue),
                                          ],
                                        ),
                                      ),
                                    )
                                    .toList(),
                              )),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(top: 20, left: 10),
            child: Text(
              "Tiêu đề",
              style: TextStyle(color: Colors.blue),
            ),
          ),
          Padding(
              padding: const EdgeInsets.only(top: 5, left: 10, right: 10),
              child: TextFormField(
                controller: controller.tieuDe,
                onSaved: (newValue) {
                  controller.tieuDe.text = newValue!;
                },
              )),
          Visibility(
            visible: controller.isLoaiTTDH != 2,
            child: const Padding(
              padding: EdgeInsets.only(top: 20, left: 10),
              child: Text(
                "Nội dung",
                style: TextStyle(color: Colors.blue),
              ),
            ),
          ),
          Visibility(
            visible: controller.isLoaiTTDH == 2,
            child: const Padding(
              padding: EdgeInsets.only(top: 20, left: 10),
              child: Text(
                "Nội dung chuyển tiếp",
                style: TextStyle(color: Colors.blue),
              ),
            ),
          ),
          Padding(
              padding: const EdgeInsets.only(top: 5, left: 10, right: 10),
              child: TextFormField(
                controller: controller.noiDung,
                onSaved: (newValue) {
                  controller.noiDung.text = newValue!;
                },
              )),
          Visibility(
            visible: controller.isLoaiTTDH == 2,
            child: const Padding(
              padding: EdgeInsets.only(top: 20, left: 10),
              child: Text(
                "Nội dung",
                style: TextStyle(color: Colors.blue),
              ),
            ),
          ),
          Visibility(
            visible: controller.isLoaiTTDH == 2,
            child: Padding(
                padding: const EdgeInsets.only(top: 5, left: 10, right: 10),
                child: TextFormField(
                  controller: controller.noiDungChuyenTiep,
                  onSaved: (newValue) {
                    controller.noiDungChuyenTiep.text = newValue!;
                  },
                )),
          ),
          Obx(() => Visibility(
                visible: controller.srcFileTtdhDinhKem.value != "",
                child: ListView(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    children: List.generate(
                        (controller.srcFileTtdhDinhKem.value == null)
                            ? 0
                            : MethodUntils.getFileChiTietTTDH(controller
                                    .srcFileTtdhDinhKem.value
                                    .split(":"))
                                .length, (index) {
                      return ListTile(
                        leading: Icon(
                          MethodUntils.getFileChiTietTTDH(controller
                                  .srcFileTtdhDinhKem.value
                                  .split(":"))[index]
                              .iconFile,
                          color: MethodUntils.getFileChiTietTTDH(controller
                                  .srcFileTtdhDinhKem.value
                                  .split(":"))[index]
                              .colorIcon,
                        ),
                        title: GestureDetector(
                          onTap: () {
                            ModalViewFileOnline.ViewFileOnline(
                                tenFile: MethodUntils.getFileName(controller
                                    .srcFileTtdhDinhKem.value
                                    .split(":")[index]
                                    .toString()),
                                path: MethodUntils.getFileChiTietTTDH(controller
                                        .srcFileTtdhDinhKem.value
                                        .split(":"))[index]
                                    .urlViewFile!,
                                item: controller.srcFileTtdhDinhKem.value
                                    .split(":")[index]);
                            // setupController.openFile(
                            //     url: MethodUntils.getFileChiTietTTDH(controller
                            //             .srcFileTtdhDinhKem.value
                            //             .split(":"))[index]
                            //         .urlViewFile!,
                            //     fileName: MethodUntils.getFileChiTietTTDH(
                            //             controller.srcFileTtdhDinhKem.value
                            //                 .split(":"))[index]
                            //         .fileName!,
                            //     indexFile: index);
                          },
                          child: Text(
                              MethodUntils.getFileName(controller
                                  .srcFileTtdhDinhKem.value
                                  .split(":")[index]
                                  .toString()),
                              style: const TextStyle(
                                  color: AppColor.blackColor, fontSize: 13)),
                        ),
                        trailing: IconButton(
                            onPressed: () {
                              controller.delFileDinhKem(controller
                                  .srcFileTtdhDinhKem.value
                                  .split(":")[index]);
                            },
                            icon: const Icon(
                              Icons.close,
                              color: Colors.red,
                            )),
                      );
                    })),
              )),
          Padding(
            padding: const EdgeInsets.only(top: 20, left: 10),
            child: ListTile(
              leading: const Icon(
                Icons.attachment_outlined,
                color: Colors.blueAccent,
              ),
              title: const Text(
                "Chọn tệp đính kèm",
                style: TextStyle(color: Colors.blueAccent),
              ),
              onTap: () async {
                await Get.defaultDialog(
                    title: "Chọn văn bản đính kèm",
                    titleStyle: const TextStyle(fontSize: 14),
                    content: Column(
                      children: [
                        GestureDetector(
                          onTap: () {
                            controller.pickFile();
                          },
                          child: SvgPicture.asset(
                            'svg/upload.svg',
                            height: 80,
                            width: 80,
                          ),
                        ),
                        if (controller.selectedFile.value != null)
                          Obx(() => Text(controller.selectedFileName.value!)),
                      ],
                    ),
                    actions: [
                      ElevatedButton(
                        onPressed: () {
                          controller.selectedFile.value = File("");
                          controller.selectedFileName.value = '';
                          Get.back();
                        },
                        child: const Text("Đóng"),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          controller.upLoadFileDinhKem();
                          // Close the dialog without uploading.
                        },
                        child: const Text("Xác nhận"),
                      ),
                    ]);
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10, left: 10),
            child: Row(
              children: [
                Obx(() => Checkbox(
                    value: controller.isSms.value,
                    onChanged: (value) {
                      controller.changeIsSms(value!);
                    })),
                const Text(
                  "sms",
                  style: TextStyle(color: Colors.blueAccent),
                ),
              ],
            ),
          ),
          Visibility(
            visible: (controller.isLoaiTTDH == 2 || controller.isLoaiTTDH == 3),
            child: Center(
              child: ElevatedButton.icon(
                onPressed: () {
                  showModalBottomSheet(
                      shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10))),
                      context: context,
                      builder: (BuildContext context) {
                        return Container(
                          child: Column(children: [
                            Container(
                                color: AppColor.blueAccentColor,
                                child: TabBar(
                                    indicatorColor: Colors.white,
                                    unselectedLabelColor: Colors.grey,
                                    labelColor: AppColor.whiteColor,
                                    controller: controller.tabTreeController,
                                    tabs: controller.tabsTree)),
                            Expanded(
                              flex: 1,
                              child: TabBarView(
                                controller: controller.tabTreeController,
                                children: [
                                  Container(
                                    color: Colors.white,
                                    child: Column(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(5),
                                          color: Colors.grey[200],
                                          child: const Center(
                                              child: Text("Họ tên")),
                                        ),
                                        Expanded(
                                            flex: 1,
                                            child: SingleChildScrollView(
                                              child: TreeViewDsCbTTDH(
                                                xulyTtdhController: controller,
                                              ),
                                            ))
                                      ],
                                    ),
                                  ),
                                  Container(
                                    color: Colors.white,
                                    child: Column(
                                      children: [
                                        Expanded(
                                            flex: 1,
                                            child: SingleChildScrollView(
                                              child: TreeViewNhomCBNTTDH(
                                                  xulyController: controller),
                                            ))
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ]),
                        );
                      });
                },
                style: ButtonStyle(
                    backgroundColor:
                        MaterialStateProperty.all<Color>(Colors.blue)),
                icon: const Icon(
                  Icons.groups_2_outlined,
                  color: Colors.white,
                ),
                label: const Text(
                  "Chọn đơn vị/Cán bộ",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          ),
        ]),
      ),
    );
  }
}
