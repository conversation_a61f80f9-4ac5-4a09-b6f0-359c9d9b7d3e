import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/vbnb_controller.dart';
import 'package:badges/badges.dart' as badges;
import 'package:vnpt_ioffice_camau/modules/views/ttdh/ttdh_item.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbnb/item_list_vbnb.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbnb/item_listdagui_vbnb.dart';

class ThongTinDieuHanh extends GetView<ThongTinDieuHanhController> {
  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 240,
              height: 45,
              child: TabBar(
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      5.0,
                    ),
                    color: AppColor.blueAccentColor,
                  ),
                  unselectedLabelColor: Colors.black,
                  padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                  labelColor: AppColor.whiteColor,
                  controller: controller.tabController,
                  onTap: (value) {
                    controller.changeIndexTab(value);
                  },
                  tabs: [Tab(text: "Đã nhận"), const Tab(text: "Đã gửi")]),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.all(5.0),
          child: SizedBox(
            height: 45,
            child: TextFormField(
                cursorColor: const Color.fromARGB(255, 242, 237, 237),
                style: const TextStyle(color: AppColor.blackColor),
                onFieldSubmitted: (value) {
                  controller.searchThongTinDieuHanh(value);
                },
                decoration: const InputDecoration(
                    errorStyle: TextStyle(color: AppColor.helpBlue),
                    border: OutlineInputBorder(
                      borderSide:
                          BorderSide(width: 1, color: AppColor.helpBlue),
                    ),
                    labelStyle: TextStyle(color: AppColor.helpBlue),
                    focusColor: AppColor.blackColor,
                    prefixIcon: Icon(
                      Icons.search_outlined,
                      color: AppColor.helpBlue,
                      size: 20,
                    ),
                    hintText: "Nhập từ khoá tìm kiếm...",
                    hintStyle: TextStyle(fontSize: 13))),
          ),
        ),
        Expanded(
            child: TabBarView(
                controller: controller.tabController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
              Container(
                child: Column(
                  children: [
                    Expanded(
                        child: Container(
                      color: AppColor.whiteColor,
                      child: controller.obx(
                          (dsThongTinDieuHanh) => controller.isLoadData.value
                              ? ListView.builder(
                                  controller: controller.scrollerTTDHdaNhan,
                                  physics: const BouncingScrollPhysics(),
                                  itemCount: controller.listTtdhDaNhan.length,
                                  itemBuilder: (context, index) =>
                                      ItemThongTinDieuHanh(
                                    tieuDe: controller
                                        .listTtdhDaNhan[index].tieuDe!,
                                    NoiDung: controller
                                        .listTtdhDaNhan[index].noiDung!,
                                    ngayBanHanh: controller
                                        .listTtdhDaNhan[index].ngayNhan
                                        .toString(),
                                    onClickItem: () {
                                      controller.onPressToPageDetail(
                                          controller
                                              .listTtdhDaNhan[index].maTtdhKc
                                              ?.toInt(),
                                          controller
                                              .listTtdhDaNhan[index].maTtdhGuiKc
                                              ?.toInt(),
                                          0,
                                          controller.listTtdhDaNhan[index]);
                                    },
                                  ),
                                )
                              : const Text(""),
                          onLoading: SpinKitCircle(
                            color: AppColor.blueAccentColor,
                          ),
                          onEmpty: Container(
                            child: Center(
                                child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Lottie.asset("lottie/emptyBox.json",
                                    height: 200, width: 200),
                                const Text("Không tìm thấy dữ liệu!")
                              ],
                            )),
                          )),
                    ))
                  ],
                ),
              ),
              Container(
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                          color: AppColor.whiteColor,
                          child: controller.obx(
                              (dsThongTinDieuHanhDaGui) => controller
                                      .isLoadData.value
                                  ? ListView.builder(
                                      controller: controller.scrollerTTDHdaGui,
                                      physics: const BouncingScrollPhysics(),
                                      itemCount:
                                          controller.listTtdhDaGui.length,
                                      itemBuilder: (context, index) =>
                                          ItemThongTinDieuHanh(
                                        tieuDe: controller
                                            .listTtdhDaGui[index].tieuDe!,
                                        NoiDung: controller
                                                .listTtdhDaGui[index].noiDung ??
                                            "",
                                        ngayBanHanh: controller
                                            .listTtdhDaGui[index].ngayNhan
                                            .toString(),
                                        onClickItem: () {
                                          controller.onPressToPageDetail(
                                              controller
                                                  .listTtdhDaGui[index].maTtdhKc
                                                  ?.toInt(),
                                              controller.listTtdhDaGui[index]
                                                  .maTtdhGuiKc
                                                  ?.toInt(),
                                              1,
                                              controller.listTtdhDaGui[index]);
                                        },
                                      ),
                                    )
                                  : const Text(""),
                              onLoading: SpinKitCircle(
                                color: AppColor.blueAccentColor,
                              ),
                              onEmpty: Container(
                                child: Center(
                                    child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Lottie.asset("lottie/emptyBox.json",
                                        height: 200, width: 200),
                                    const Text("Không tìm thấy dữ liệu!")
                                  ],
                                )),
                              ))),
                    )
                  ],
                ),
              ),
            ]))
      ],
    ));
  }
}
