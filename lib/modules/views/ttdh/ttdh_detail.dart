import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_detail_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class ThongTinDieuHanhDetail extends GetView<TTDHDetailController> {
  SetupController setupController = Get.find();
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
        ),
        child: Column(
          children: [
            Container(
              height: 50,
              child: Obx(() => Center(
                      child: Text(
                    controller.listDanhSachChiTiet.value.isNotEmpty
                        ? controller.listDanhSachChiTiet[0]!.tieuDe!
                        : "",
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ))),
            ),
            Obx(() => Column(
                  children: controller.listDanhSachChiTiet
                      .map((element) => Card(
                            color: Colors.white,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ListTile(
                                  title: Text(element.tenNguoiGui ?? ""),
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                          "Loai: ${element.tenLoaiTtdh ?? ""}"),
                                      const Text("Người nhận:"),
                                      FutureBuilder<Widget>(
                                        future: controller.getNguoiNhan(
                                            element.maTtdhKc!.toInt()),
                                        builder: ((context, snapshot) {
                                          if (snapshot.connectionState ==
                                              ConnectionState.waiting) {
                                            return const CircularProgressIndicator();
                                          } else {
                                            return snapshot.data!;
                                          }
                                        }),
                                      ),
                                    ],
                                  ),
                                  trailing: Text(DateFormat('dd/MM/yyyy')
                                          .format(element.ngayGui!) ??
                                      ""),
                                ),
                                // danh sách file

                                ListView(
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    children: List.generate(
                                        (element.srcFileTtdh == null)
                                            ? 0
                                            : MethodUntils.getFileChiTietTTDH(
                                                    element.srcFileTtdh!
                                                        .split(":"))
                                                .length, (index) {
                                      return ListTile(
                                        leading: Icon(
                                          MethodUntils.getFileChiTietTTDH(
                                                  element.srcFileTtdh!
                                                      .split(":"))[index]
                                              .iconFile,
                                          color:
                                              MethodUntils.getFileChiTietTTDH(
                                                      element.srcFileTtdh!
                                                          .split(":"))[index]
                                                  .colorIcon,
                                        ),
                                        title: GestureDetector(
                                          onTap: () {
                                            ModalViewFileOnline.ViewFileOnline(
                                                tenFile: MethodUntils
                                                        .getFileChiTietTTDH(
                                                            element
                                                                .srcFileTtdh!
                                                                .split(
                                                                    ":"))[index]
                                                    .fileName!,
                                                item: element.srcFileTtdh!
                                                    .split(":")[index],
                                                path: MethodUntils.getFileChiTietTTDH(
                                                        element.srcFileTtdh!
                                                            .split(":"))[index]
                                                    .urlViewFile!);
                                            // setupController.openFile(
                                            //     url: MethodUntils
                                            //             .getFileChiTietTTDH(
                                            //                 element
                                            //                     .srcFileTtdh!
                                            //                     .split(
                                            //                         ":"))[index]
                                            //         .urlViewFile!,
                                            //     fileName: MethodUntils
                                            //             .getFileChiTietTTDH(
                                            //                 element
                                            //                     .srcFileTtdh!
                                            //                     .split(
                                            //                         ":"))[index]
                                            //         .fileName!,
                                            //     indexFile: index);
                                          },
                                          child: Text(
                                              MethodUntils.getFileName(element
                                                  .srcFileTtdh!
                                                  .split(":")[index]
                                                  .toString()),
                                              style: const TextStyle(
                                                  color: AppColor.blackColor,
                                                  fontSize: 13)),
                                        ),
                                      );
                                    })),

                                Padding(
                                  padding: const EdgeInsets.all(10),
                                  child: Text(
                                    MethodUntils.removeNoiDungTTDH(
                                        element.noiDung ?? ""),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                ),
                                const Divider(
                                  height: 2,
                                  color: AppColor.greyColor,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Visibility(
                                      visible: controller.store
                                              .read(GetStorageKey.maCanBo) !=
                                          element.maCanBoGui,
                                      child: ElevatedButton.icon(
                                          style: ButtonStyle(
                                              side: MaterialStateProperty.all<
                                                  BorderSide>(
                                                const BorderSide(
                                                    width: 1,
                                                    color: Colors
                                                        .blueGrey), // Define the border here
                                              ),
                                              backgroundColor:
                                                  MaterialStateProperty.all<
                                                      Color>(Colors.white)),
                                          onPressed: () {
                                            Get.toNamed(Routers.TTDHPHANHOI,
                                                arguments: {
                                                  'listDanhSachChiTiet':
                                                      controller
                                                          .listDanhSachChiTiet,
                                                  'itemTtdh': controller.item,
                                                  'listNguoiNhan':
                                                      controller.listNguoiNhan,
                                                  'isLoaiTTDH': 0
                                                });
                                          },
                                          icon: Icon(
                                            Icons.reply,
                                            color: AppColor.blueAccentColor,
                                          ),
                                          label: const Text(
                                            "Phản hồi",
                                            style:
                                                TextStyle(color: Colors.black),
                                          )),
                                    ),
                                    Visibility(
                                      visible: controller.store
                                              .read(GetStorageKey.maCanBo) !=
                                          element.maCanBoGui,
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(left: 10),
                                        child: ElevatedButton.icon(
                                            style: ButtonStyle(
                                                side: MaterialStateProperty.all<
                                                    BorderSide>(
                                                  const BorderSide(
                                                      width: 1,
                                                      color: Colors
                                                          .blueGrey), // Define the border here
                                                ),
                                                backgroundColor:
                                                    MaterialStateProperty.all<
                                                        Color>(Colors.white)),
                                            onPressed: () {
                                              Get.toNamed(Routers.TTDHPHANHOI,
                                                  arguments: {
                                                    'listDanhSachChiTiet':
                                                        controller
                                                            .listDanhSachChiTiet,
                                                    'itemTtdh': controller.item,
                                                    'listNguoiNhan': controller
                                                        .listNguoiNhan,
                                                    'isLoaiTTDH': 1
                                                  });
                                            },
                                            icon: Icon(
                                              Icons.reply_all,
                                              color: AppColor.blueAccentColor,
                                            ),
                                            label: const Text(
                                              "Phản hồi tất cả",
                                              style: TextStyle(
                                                  color: Colors.black),
                                            )),
                                      ),
                                    )
                                  ],
                                ),
                                Visibility(
                                  visible: controller.store
                                          .read(GetStorageKey.maCanBo) !=
                                      element.maCanBoGui,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      ElevatedButton.icon(
                                          onPressed: () {
                                            Get.toNamed(Routers.TTDHPHANHOI,
                                                arguments: {
                                                  'listDanhSachChiTiet':
                                                      controller
                                                          .listDanhSachChiTiet,
                                                  'itemTtdh': controller.item,
                                                  'listNguoiNhan':
                                                      controller.listNguoiNhan,
                                                  'isLoaiTTDH': 2
                                                });
                                          },
                                          style: ButtonStyle(
                                              side: MaterialStateProperty.all<
                                                  BorderSide>(
                                                const BorderSide(
                                                    width: 1,
                                                    color: Colors
                                                        .blueGrey), // Define the border here
                                              ),
                                              backgroundColor:
                                                  MaterialStateProperty.all<
                                                      Color>(Colors.blue)),
                                          icon: const Icon(Icons.shortcut,
                                              color: Colors.white),
                                          label: const Text("Chuyển tiếp",
                                              style: TextStyle(
                                                  color: Colors.white))),
                                    ],
                                  ),
                                ),
                                Visibility(
                                  visible: controller.store
                                          .read(GetStorageKey.maCanBo) ==
                                      element.maCanBoGui,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      ElevatedButton.icon(
                                          onPressed: () {
                                            Get.toNamed(Routers.TTDHPHANHOI,
                                                arguments: {
                                                  'listDanhSachChiTiet':
                                                      controller
                                                          .listDanhSachChiTiet,
                                                  'itemTtdh': controller.item,
                                                  'listNguoiNhan':
                                                      controller.listNguoiNhan,
                                                  'isLoaiTTDH': 2
                                                });
                                          },
                                          style: ButtonStyle(
                                              side: MaterialStateProperty.all<
                                                  BorderSide>(
                                                const BorderSide(
                                                    width: 1,
                                                    color: Colors
                                                        .blueGrey), // Define the border here
                                              ),
                                              backgroundColor:
                                                  MaterialStateProperty.all<
                                                      Color>(Colors.blue)),
                                          icon: const Icon(Icons.shortcut,
                                              color: Colors.white),
                                          label: const Text("Chuyển tiếp",
                                              style: TextStyle(
                                                  color: Colors.white))),
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ))
                      .toList(),
                )),
          ],
        ),
      ),
    );
  }
}
