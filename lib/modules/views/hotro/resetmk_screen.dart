import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/resetmk_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/hotro/widgets/unit_tree_selector.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/staff_model.dart';

class ResetMatKhauNguoiDung extends GetView<ResetMkNguoiDungController> {
  const ResetMatKhauNguoiDung({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Chọn đơn vị
                  _buildUnitSelector(),
                  const SizedBox(height: 24),

                  // <PERSON>h sách cán bộ
                  Obx(() => _buildStaffList()),
                ],
              ),
            ),

            // Unit tree dialog
            Obx(() => controller.showUnitTreeDialog.value
                ? UnitTreeSelector(controller: controller)
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitSelector() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_tree,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Chọn đơn vị',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Selected unit display
          Obx(() => GestureDetector(
                onTap: () => controller.showUnitSelector(),
                child: Container(
                  width: Get.width,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.grey[50],
                  ),
                  child: Row(
                    children: [
                      Icon(
                        controller.selectedUnitId.value == 0
                            ? Icons.business_outlined
                            : Icons.business,
                        color: controller.selectedUnitId.value == 0
                            ? Colors.grey[500]
                            : AppColor.blueAccentColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          controller.selectedUnitId.value == 0
                              ? '-- Nhấn để chọn đơn vị --'
                              : controller.selectedUnitName.value,
                          style: GoogleFonts.inter(
                            fontSize: 16,
                            color: controller.selectedUnitId.value == 0
                                ? Colors.grey[600]
                                : Colors.grey[800],
                            fontWeight: controller.selectedUnitId.value == 0
                                ? FontWeight.w400
                                : FontWeight.w500,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey[500],
                        size: 20,
                      ),
                    ],
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildStaffList() {
    if (controller.selectedUnitId.value == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.people,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Danh sách cán bộ (${controller.staffList.length} người)',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (controller.isLoadingStaff.value)
            _buildLoadingState()
          else if (controller.staffList.isEmpty)
            _buildEmptyState()
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.staffList.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final staff = controller.staffList[index];
                return _buildStaffItem(staff);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildStaffItem(StaffModel staff) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[200]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.indigo,
                  Colors.purple.shade400,
                ],
              ),
            ),
            child: const Icon(
              Icons.person,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Thông tin cán bộ
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  staff.hoVaTenCanBo,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  staff.tenChucVu,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Username: ${staff.username}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
                if (staff.hasPhoneNumber())
                  Text(
                    'SĐT: ${staff.getPhoneNumber()}',
                    style: GoogleFonts.inter(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
              ],
            ),
          ),

          // Reset button
          Obx(() => ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: controller.isResetting.value &&
                          controller.resettingStaffId.value ==
                              staff.maCtcbKc.toInt()
                      ? Colors.grey[300]
                      : Colors.red[600],
                  foregroundColor: controller.isResetting.value &&
                          controller.resettingStaffId.value ==
                              staff.maCtcbKc.toInt()
                      ? Colors.grey[600]
                      : Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: controller.isResetting.value &&
                        controller.resettingStaffId.value ==
                            staff.maCtcbKc.toInt()
                    ? null
                    : () => controller.resetPassword(staff),
                icon: controller.isResetting.value &&
                        controller.resettingStaffId.value ==
                            staff.maCtcbKc.toInt()
                    ? const SpinKitThreeBounce(
                        color: Colors.grey,
                        size: 16,
                      )
                    : const Icon(Icons.refresh, size: 16),
                label: Text(
                  controller.isResetting.value &&
                          controller.resettingStaffId.value ==
                              staff.maCtcbKc.toInt()
                      ? 'Đang xử lý...'
                      : 'Reset mật khẩu',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Không có cán bộ nào trong đơn vị này',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Đang tải danh sách cán bộ...',
            style: GoogleFonts.inter(
              fontSize: 16,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
