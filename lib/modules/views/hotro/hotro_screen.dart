import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:load_switch/load_switch.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/hotro_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/user_controller.dart';

class HoTroNguoiDung extends GetView<HoTroController> {
  const HoTroNguoiDung({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>umn(
          children: [
            // Chọn đơn vị
            _buildUnitSelector(),
            const SizedBox(height: 16),
            // Search unit toggle button
            Row(
              children: [
                Expanded(child: Container()),
                TextButton.icon(
                  onPressed: () {
                    controller.isUnitSearchVisible.value =
                        !controller.isUnitSearchVisible.value;
                    if (!controller.isUnitSearchVisible.value) {
                      controller.unitSearchController.clear();
                      // controller.filteredUnits = List.from(flattenedUnits);
                    }
                  },
                  icon: Icon(
                    controller.isUnitSearchVisible.value
                        ? Icons.close
                        : Icons.search,
                    size: 18,
                  ),
                  label: Text(
                    controller.isUnitSearchVisible.value
                        ? 'Đóng tìm kiếm'
                        : 'Tìm kiếm đơn vị',
                    style: GoogleFonts.inter(fontSize: 14),
                  ),
                ),
              ],
            ),

            // Unit search field
            if (controller.isUnitSearchVisible.value) ...[
              const SizedBox(height: 12),
              TextField(
                controller: controller.unitSearchController,
                decoration: InputDecoration(
                  hintText: 'Tìm kiếm đơn vị...',
                  hintStyle: GoogleFonts.inter(color: Colors.grey[500]),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColor.blueAccentColor),
                  ),
                  prefixIcon: const Icon(Icons.search, size: 20),
                  suffixIcon: controller.unitSearchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, size: 20),
                          onPressed: () {
                            controller.unitSearchController.clear();
                            controller.searchUnits('');
                          },
                        )
                      : null,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  isDense: true,
                ),
                onChanged: controller.searchUnits,
              ),
              const SizedBox(height: 12),
            ],

            // Tìm kiếm
            _buildSearchSection(),
            const SizedBox(height: 16),

            // Danh sách văn bản
            _buildDocumentList(),
          ],
        ),
      ),
    );
  }

  Widget _buildUnitSelector() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Chọn đơn vị',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Searchable unit selector
          GestureDetector(
            onTap: () {},
            child: Container(
              width: Get.width,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: controller.showUnitDropdown
                      ? AppColor.blueAccentColor
                      : Colors.grey[300]!,
                  width: controller.showUnitDropdown ? 2 : 1,
                ),
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      controller.selectedUnitName.isEmpty
                          ? '-- Chọn đơn vị --'
                          : controller.selectedUnitName,
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        color: controller.selectedUnitName.isEmpty
                            ? Colors.grey[600]
                            : Colors.grey[800],
                        fontWeight: controller.selectedUnitName.isEmpty
                            ? FontWeight.normal
                            : FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(
                    controller.showUnitDropdown
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),

          // Dropdown list với search
          if (controller.showUnitDropdown) ...[
            const SizedBox(height: 8),
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(12),
                color: Colors.white,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Search trong dropdown
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: TextField(
                      controller: controller.unitSearchController,
                      decoration: InputDecoration(
                        hintText: 'Tìm kiếm đơn vị...',
                        hintStyle: GoogleFonts.inter(
                            color: Colors.grey[500], fontSize: 14),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(color: Colors.grey[300]!),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide:
                              BorderSide(color: AppColor.blueAccentColor),
                        ),
                        prefixIcon: const Icon(Icons.search, size: 18),
                        suffixIcon:
                            controller.unitSearchController.text.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear, size: 18),
                                    onPressed: () {
                                      controller.unitSearchController.clear();
                                      controller.searchUnits('');
                                    },
                                  )
                                : null,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        isDense: true,
                      ),
                      onChanged: controller.searchUnits,
                      autofocus: true,
                    ),
                  ),
                  // Danh sách đơn vị
                  Flexible(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: controller.filteredUnits.length,
                      itemBuilder: (context, index) {
                        final unit = controller.filteredUnits[index];
                        return ListTile(
                          dense: true,
                          contentPadding: EdgeInsets.only(
                            left: (16 + (unit['level'] * 20.0)).toDouble(),
                            right: 16,
                            top: 2,
                            bottom: 2,
                          ),
                          leading: unit['level'] > 0
                              ? Icon(
                                  unit['level'] == 1
                                      ? Icons.subdirectory_arrow_right
                                      : Icons.more_horiz,
                                  size: 16,
                                  color: Colors.grey[600],
                                )
                              : Icon(
                                  Icons.account_balance,
                                  size: 18,
                                  color: AppColor.blueAccentColor,
                                ),
                          title: Text(
                            unit['name'],
                            style: GoogleFonts.inter(
                              fontSize: unit['level'] == 0 ? 15 : 14,
                              fontWeight: unit['level'] == 0
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: unit['level'] == 0
                                  ? AppColor.blueAccentColor
                                  : Colors.grey[700],
                            ),
                          ),
                          onTap: () =>
                              controller.selectUnit(unit['id'], unit['name']),
                          selected: controller.selectedUnitId == unit['id'],
                          selectedTileColor:
                              AppColor.blueAccentColor.withOpacity(0.1),
                          hoverColor: Colors.grey[100],
                        );
                      },
                    ),
                  ),
                  if (controller.filteredUnits.isEmpty)
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Text(
                        'Không tìm thấy đơn vị',
                        style: GoogleFonts.inter(
                          color: Colors.grey[500],
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.search,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Tìm kiếm văn bản',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller.searchController,
                  decoration: InputDecoration(
                    hintText: 'Nhập ký hiệu văn bản (VD: 01/QĐ-UBND)',
                    hintStyle: GoogleFonts.inter(color: Colors.grey[500]),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppColor.blueAccentColor),
                    ),
                    prefixIcon: const Icon(Icons.article_outlined),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                  onSubmitted: (_) => controller.searchDocuments(),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.blueAccentColor,
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: controller.isSearching.value
                    ? null
                    : controller.searchDocuments,
                icon: controller.isSearching.value
                    ? const SpinKitThreeBounce(color: Colors.white, size: 16)
                    : const Icon(Icons.search, size: 20),
                label: Text(
                  controller.isSearching.value ? 'Đang tìm...' : 'Tìm kiếm',
                  style: GoogleFonts.inter(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentList() {
    if (controller.documentList.isEmpty &&
        controller.selectedUnitId != 0 &&
        !controller.isSearching.value) {
      return _buildEmptyState();
    }

    if (controller.documentList.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                color: AppColor.blueAccentColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Danh sách văn bản (${controller.documentList.length} văn bản)',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.documentList.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final document = controller.documentList[index];
              return _buildDocumentItem(document);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(Map<String, dynamic> document) {
    bool canRecall = document['trangThai'] != 'Đã thu hồi';
    bool isCurrentlyRecalling = controller.isRecalling.value &&
        controller.recallingDocId == document['id'];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[200]!),
        borderRadius: BorderRadius.circular(12),
        color: canRecall ? Colors.white : Colors.grey[50],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header với ký hiệu và trạng thái
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColor.blueAccentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  document['kyHieu'],
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColor.blueAccentColor,
                  ),
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: canRecall ? Colors.green[100] : Colors.red[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  document['trangThai'],
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: canRecall ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Tiêu đề
          Text(
            document['tieuDe'],
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),

          // Thông tin chi tiết
          Row(
            children: [
              _buildInfoChip(Icons.category, document['loaiVanBan']),
              const SizedBox(width: 12),
              _buildInfoChip(Icons.calendar_today, document['ngayBanHanh']),
            ],
          ),
          const SizedBox(height: 8),
          _buildInfoChip(Icons.person, document['nguoiKy']),
          const SizedBox(height: 16),

          // Button thu hồi
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: isCurrentlyRecalling || !canRecall
                      ? Colors.grey[300]
                      : Colors.red[600],
                  foregroundColor: isCurrentlyRecalling || !canRecall
                      ? Colors.grey[600]
                      : Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: isCurrentlyRecalling || !canRecall
                    ? null
                    : () => controller.recallDocument(document),
                icon: isCurrentlyRecalling
                    ? const SpinKitThreeBounce(color: Colors.grey, size: 16)
                    : Icon(
                        canRecall ? Icons.cancel : Icons.check_circle,
                        size: 16,
                      ),
                label: Text(
                  isCurrentlyRecalling
                      ? 'Đang thu hồi...'
                      : canRecall
                          ? 'Thu hồi'
                          : 'Đã thu hồi',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          text,
          style: GoogleFonts.inter(
            fontSize: 13,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Không tìm thấy văn bản',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Hãy thử tìm kiếm với ký hiệu khác',
            style: GoogleFonts.inter(
              fontSize: 14,
              color: Colors.grey[400],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
