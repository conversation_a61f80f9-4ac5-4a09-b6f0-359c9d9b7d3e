import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/resetmk_controller.dart';

class UnitTreeSelector extends StatelessWidget {
  final ResetMkNguoiDungController controller;

  const UnitTreeSelector({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: Get.width * 0.9,
        height: Get.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.account_tree,
                  color: AppColor.blueAccentColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Chọn đơn vị',
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => controller.hideUnitSelector(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // Tree view
            Expanded(
              child: SingleChildScrollView(
                child: Obx(() => _buildTreeNode(controller.unitTree.value)),
              ),
            ),

            // Footer buttons
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => controller.hideUnitSelector(),
                  child: Text(
                    'Hủy',
                    style: GoogleFonts.inter(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTreeNode(UnitTreeNode node) {
    return Obx(() => Column(
          children: [
            GestureDetector(
              onTap: () {
                if (node.children.isNotEmpty) {
                  controller.toggleUnitExpansion(node);
                } else {
                  // Chọn đơn vị nếu không có con
                  controller.selectUnit(node.id, node.name);
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                margin: const EdgeInsets.symmetric(vertical: 2),
                decoration: BoxDecoration(
                  color: node.selected.value
                      ? AppColor.blueAccentColor.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: node.selected.value
                      ? Border.all(color: AppColor.blueAccentColor, width: 1)
                      : null,
                ),
                child: Row(
                  children: [
                    // Expand/collapse icon
                    SizedBox(
                      width: 24,
                      child: node.children.isNotEmpty
                          ? Icon(
                              node.expanded.value
                                  ? Icons.keyboard_arrow_down
                                  : Icons.keyboard_arrow_right,
                              color: Colors.grey[600],
                              size: 20,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),

                    // Unit icon
                    Icon(
                      node.children.isNotEmpty
                          ? Icons.folder_outlined
                          : Icons.business_outlined,
                      color: node.selected.value
                          ? AppColor.blueAccentColor
                          : Colors.grey[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),

                    // Unit name
                    Expanded(
                      child: Text(
                        node.name,
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: node.selected.value
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: node.selected.value
                              ? AppColor.blueAccentColor
                              : Colors.grey[800],
                        ),
                      ),
                    ),

                    // Select button for leaf nodes
                    if (node.children.isEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColor.blueAccentColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Chọn',
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Children nodes
            if (node.expanded.value && node.children.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 24),
                child: Column(
                  children: node.children
                      .map((child) => _buildTreeNode(child))
                      .toList(),
                ),
              ),
          ],
        ));
  }
}
