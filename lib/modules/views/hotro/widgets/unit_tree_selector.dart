import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/resetmk_controller.dart';
import 'package:vnpt_ioffice_camau/app/model/hotro/jsTree_model.dart';

class UnitTreeSelector extends StatelessWidget {
  final ResetMkNguoiDungController controller;

  const UnitTreeSelector({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: Get.width * 0.9,
        height: Get.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.account_tree,
                  color: AppColor.blueAccentColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Chọn đơn vị',
                    style: GoogleFonts.inter(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => controller.hideUnitSelector(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 16),

            // Search input
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(12),
                color: Colors.grey[50],
              ),
              child: TextField(
                onChanged: (value) => controller.searchUnits(value),
                decoration: InputDecoration(
                  hintText: 'Nhập tên đơn vị để tìm kiếm...',
                  hintStyle: GoogleFonts.inter(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                  border: InputBorder.none,
                  prefixIcon: Icon(
                    Icons.search,
                    color: Colors.grey[500],
                    size: 20,
                  ),
                  suffixIcon: Obx(() => controller.searchQuery.value.isNotEmpty
                      ? IconButton(
                          onPressed: () => controller.searchUnits(''),
                          icon: Icon(
                            Icons.clear,
                            color: Colors.grey[500],
                            size: 20,
                          ),
                        )
                      : const SizedBox.shrink()),
                ),
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: Colors.grey[800],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Tree view
            Expanded(
              child: Obx(() {
                if (controller.isLoadingUnits.value) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Đang tải dữ liệu đơn vị...'),
                      ],
                    ),
                  );
                }

                return SingleChildScrollView(
                  child: _buildTreeNode(controller.filteredUnitTree.value),
                );
              }),
            ),

            // Footer buttons
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Cancel button
                TextButton(
                  onPressed: () => controller.hideUnitSelector(),
                  child: Text(
                    'Hủy',
                    style: GoogleFonts.inter(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTreeNode(UnitTreeNode node) {
    return Obx(() => Column(
          children: [
            GestureDetector(
              onTap: () {
                if (node.children.isNotEmpty) {
                  controller.toggleUnitExpansion(node);
                } else {
                  // Chọn đơn vị nếu không có con
                  controller.selectUnit(node.id, node.name);
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                margin: const EdgeInsets.symmetric(vertical: 2),
                decoration: BoxDecoration(
                  color: node.selected.value
                      ? AppColor.blueAccentColor.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: node.selected.value
                      ? Border.all(color: AppColor.blueAccentColor, width: 1)
                      : null,
                ),
                child: Row(
                  children: [
                    // Expand/collapse icon
                    SizedBox(
                      width: 24,
                      child: node.children.isNotEmpty
                          ? Icon(
                              node.expanded.value
                                  ? Icons.keyboard_arrow_down
                                  : Icons.keyboard_arrow_right,
                              color: Colors.grey[600],
                              size: 20,
                            )
                          : null,
                    ),
                    const SizedBox(width: 8),

                    // Unit icon
                    Icon(
                      node.children.isNotEmpty
                          ? Icons.folder_outlined
                          : Icons.business_outlined,
                      color: node.selected.value
                          ? AppColor.blueAccentColor
                          : Colors.grey[600],
                      size: 20,
                    ),
                    const SizedBox(width: 8),

                    // Unit name with highlight
                    Expanded(
                      child: _buildHighlightedText(
                        node.name,
                        controller.searchQuery.value,
                        node.selected.value,
                      ),
                    ),

                    // Select button for leaf nodes
                    if (node.children.isEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColor.blueAccentColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Chọn',
                          style: GoogleFonts.inter(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Children nodes
            if (node.expanded.value && node.children.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(left: 24),
                child: Column(
                  children: node.children
                      .map((child) => _buildTreeNode(child))
                      .toList(),
                ),
              ),
          ],
        ));
  }

  Widget _buildHighlightedText(String text, String query, bool isSelected) {
    if (query.isEmpty) {
      return Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          color: isSelected ? AppColor.blueAccentColor : Colors.grey[800],
        ),
      );
    }

    final lowerText = text.toLowerCase();
    final lowerQuery = query.toLowerCase();
    final index = lowerText.indexOf(lowerQuery);

    if (index == -1) {
      return Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          color: isSelected ? AppColor.blueAccentColor : Colors.grey[800],
        ),
      );
    }

    return RichText(
      text: TextSpan(
        children: [
          // Text before highlight
          if (index > 0)
            TextSpan(
              text: text.substring(0, index),
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? AppColor.blueAccentColor : Colors.grey[800],
              ),
            ),
          // Highlighted text
          TextSpan(
            text: text.substring(index, index + query.length),
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w700,
              color: Colors.orange[700],
              backgroundColor: Colors.yellow[200],
            ),
          ),
          // Text after highlight
          if (index + query.length < text.length)
            TextSpan(
              text: text.substring(index + query.length),
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? AppColor.blueAccentColor : Colors.grey[800],
              ),
            ),
        ],
      ),
    );
  }
}
