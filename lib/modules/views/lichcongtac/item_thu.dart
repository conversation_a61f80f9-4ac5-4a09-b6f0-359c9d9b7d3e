import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get/utils.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_detail_dv.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/global_widget/view_file_online.dart';

class ItemThu extends StatelessWidget {
  final DateTime ngay;
  final List<LctDetaiThuDonVi?> data;

  ItemThu({required this.ngay, required this.data, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 5, right: 5, top: 5),
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(
          color: AppColor.blueAccentColor,
        )),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                color: AppColor.blueAccentColor,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        MethodUntils.getWeekdayName(ngay) ?? "",
                        style: GoogleFonts.roboto(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600),
                      ),
                      Text(
                        data.first!.ngayThucHienVn! ?? "",
                        style: GoogleFonts.roboto(
                            color: Colors.white,
                            fontSize: 13,
                            fontStyle: FontStyle.italic),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                  child: Padding(
                padding: const EdgeInsets.only(left: 5),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: data
                                .where(
                                    (element) => element!.gioThucHien != null)
                                .length >
                            0
                        ? data
                            .map((e) => e!.gioThucHien != null
                                ? Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                        Visibility(
                                          visible: e!.gioThucHien != null,
                                          child: Text(
                                            e.gioThucHien ?? "",
                                            style: GoogleFonts.roboto(
                                                color: Colors.red,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 20),
                                          ),
                                        ),
                                        Visibility(
                                          visible: e.noiDung != null,
                                          child: RichText(
                                              text: TextSpan(
                                            text: 'Nội dung: ',
                                            style: GoogleFonts.roboto(
                                                color: Colors.black,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16),
                                            children: <TextSpan>[
                                              TextSpan(
                                                  text: e.noiDung,
                                                  style: GoogleFonts.roboto(
                                                      color: Colors.grey,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 14)),
                                            ],
                                          )),
                                        ),
                                        Wrap(
                                            children: List.generate(
                                                (e.fileLct == null)
                                                    ? 0
                                                    : MethodUntils
                                                            .getFileChiTietTTDH(
                                                                e.fileLct!
                                                                    .split(":"))
                                                        .length, (index) {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                top: 5, bottom: 5),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Icon(
                                                  MethodUntils
                                                          .getFileChiTietTTDH(
                                                              e.fileLct!.split(
                                                                  ":"))[index]
                                                      .iconFile,
                                                  color: MethodUntils
                                                          .getFileChiTietTTDH(
                                                              e.fileLct!.split(
                                                                  ":"))[index]
                                                      .colorIcon,
                                                  size: 20,
                                                ),
                                                GestureDetector(
                                                  onTap: () {
                                                    ModalViewFileOnline.ViewFileOnline(
                                                        tenFile: MethodUntils.getFileChiTietTTDH(
                                                                e.fileLct!.split(
                                                                    ":"))[index]
                                                            .fileName!,
                                                        item: e.fileLct!
                                                            .split(":")[index],
                                                        path: MethodUntils
                                                                .getFileChiTietTTDH(e
                                                                    .fileLct!
                                                                    .split(
                                                                        ":"))[index]
                                                            .urlViewFile!);
                                                  },
                                                  child: Text(
                                                    MethodUntils.getFileName(e
                                                        .fileLct!
                                                        .split(":")[index]
                                                        .toString()),
                                                    style: const TextStyle(
                                                        color: AppColor
                                                            .darkRedColor,
                                                        fontSize: 10),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }))
                                      ])
                                : const SizedBox(
                                    height: 0,
                                  ))
                            .toList()
                        : [
                            const SizedBox(
                                height: 80,
                                child: Center(
                                    child: Text("Không có lịch công tác")))
                          ]),
              )),
            ),
          ],
        ),
      ),
    );
  }
}
