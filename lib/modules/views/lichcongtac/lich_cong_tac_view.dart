import 'dart:math';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/lichcongtac/lich_cong_tac_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/lichcongtac/item_ld_tuan.dart';
import 'package:vnpt_ioffice_camau/modules/views/lichcongtac/item_tuan.dart';

class LichCongTacView extends GetView<LichCongTacController> {
  LichCongTacView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10, top: 5, right: 10),
            child: <PERSON><PERSON><PERSON><PERSON>(
              height: 45,
              child: TabBar(
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      5.0,
                    ),
                    color: AppColor.blueAccentColor,
                  ),
                  unselectedLabelColor: Colors.black,
                  padding: const EdgeInsets.fromLTRB(0, 5, 0, 5),
                  labelColor: AppColor.whiteColor,
                  onTap: (value) {
                    controller.onChangeTab(value);
                  },
                  controller: controller.tabController,
                  tabs: [
                    Tab(
                      child: Text(
                        "Lãnh đạo",
                        style: GoogleFonts.roboto(),
                      ),
                    ),
                    Tab(
                      child: Text(
                        "Cơ quan",
                        style: GoogleFonts.roboto(),
                      ),
                    ),
                    Tab(
                      child: Text(
                        "Đơn vị",
                        style: GoogleFonts.roboto(),
                      ),
                    ),
                    Tab(
                      child: Text(
                        "Cá nhân",
                        style: GoogleFonts.roboto(),
                      ),
                    ),
                  ]),
            ),
          ),
          Expanded(
              child: TabBarView(
            controller: controller.tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              Container(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 10, right: 10, top: 10),
                        child: Container(
                          width: Get.width,
                          color: AppColor.whiteColor,
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: InkWell(
                                  onTap: () {
                                    showModalBottomSheet(
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10))),
                                        context: context,
                                        builder: (BuildContext context) {
                                          return Container(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Text("Chọn cơ quan",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleSmall!
                                                          .copyWith(
                                                              color: AppColor
                                                                  .greyColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold)),
                                                ),
                                                const Divider(
                                                    color: AppColor.greyColor),
                                                Expanded(
                                                  child: ListView.builder(
                                                    itemCount: controller
                                                        .dsCoQuanLct.length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final dsListCoQuanLCT =
                                                          controller
                                                                  .dsCoQuanLct[
                                                              index];
                                                      return Container(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            GestureDetector(
                                                              child: Text(
                                                                  "${dsListCoQuanLCT.tenDonVi}",
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  style: GoogleFonts
                                                                      .roboto(
                                                                          color:
                                                                              AppColor.helpBlue)),
                                                              onTap: () {
                                                                controller
                                                                    .changeCoQuanLctLd(
                                                                        dsListCoQuanLCT);
                                                              },
                                                            ),
                                                            const Divider(
                                                                color: AppColor
                                                                    .greyColor),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        });
                                  },
                                  child: Container(
                                    color: AppColor.blueAccentColor,
                                    child: Padding(
                                      padding: const EdgeInsets.all(10),
                                      child: Obx(
                                        () => Text(
                                          controller
                                              .selectedCoQuan.value.tenDonVi
                                              .toString(),
                                          overflow: TextOverflow.ellipsis,
                                          style: GoogleFonts.roboto(
                                              color: Colors.white,
                                              fontSize: 20,
                                              fontStyle: FontStyle.italic),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: DropdownButtonFormField2<int>(
                                  isExpanded: true,
                                  decoration: const InputDecoration(
                                    contentPadding:
                                        EdgeInsets.symmetric(vertical: 10),
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1, color: Colors.white),
                                    ),
                                  ),
                                  hint: const Text(
                                    'Năm',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                  value: controller.selectYear.value,
                                  items: controller.years
                                      .map((item) => DropdownMenuItem<int>(
                                            value: item,
                                            child: Text(
                                              item.toString(),
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black),
                                            ),
                                          ))
                                      .toList(),
                                  validator: (value) {
                                    if (value == null) {
                                      return 'Please select gender.';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    controller.selectYear.value = value!;
                                    controller.loadDataInt();
                                    //Do something when selected item is changed.
                                  },
                                  onSaved: (value) {
                                    controller.selectYear.value = value!;
                                  },
                                  buttonStyleData: const ButtonStyleData(
                                    padding: EdgeInsets.only(right: 8),
                                  ),
                                  iconStyleData: const IconStyleData(
                                    icon: Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.black,
                                    ),
                                    iconSize: 24,
                                  ),
                                  dropdownStyleData: DropdownStyleData(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                  ),
                                  menuItemStyleData: MenuItemStyleData(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  controller.subMothds();
                                },
                                icon: Icon(Icons.arrow_back_ios_new,
                                    color: AppColor.blueAccentColor, size: 20)),
                            Obx(() => Text(
                                  'Tháng ${controller.moths.toString()}',
                                  style: GoogleFonts.roboto(
                                      color: AppColor.blueAccentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                )),
                            IconButton(
                                onPressed: () {
                                  controller.addMothds();
                                },
                                icon: Icon(Icons.arrow_forward_ios,
                                    color: AppColor.blueAccentColor, size: 20)),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Obx(
                        () => Wrap(
                          spacing: 5.0,
                          runSpacing: 5.0,
                          children: controller.listWeekByMoth.value
                              .map((item) => ItemLdTuan(
                                  tuan: item.tuan!.toInt().toString(),
                                  ngayBatDau: item.start,
                                  ngayKetThuc: item.end))
                              .toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 10, right: 10, top: 10),
                        child: Container(
                          width: Get.width,
                          color: AppColor.whiteColor,
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: InkWell(
                                  onTap: () {
                                    showModalBottomSheet(
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10))),
                                        context: context,
                                        builder: (BuildContext context) {
                                          return Container(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Text("Chọn cơ quan",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleSmall!
                                                          .copyWith(
                                                              color: AppColor
                                                                  .greyColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold)),
                                                ),
                                                const Divider(
                                                    color: AppColor.greyColor),
                                                Expanded(
                                                  child: ListView.builder(
                                                    itemCount: controller
                                                        .dsCoQuanLct.length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final dsListCoQuanLCT =
                                                          controller
                                                                  .dsCoQuanLct[
                                                              index];
                                                      return Container(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            GestureDetector(
                                                              child: Text(
                                                                  "${dsListCoQuanLCT.tenDonVi}",
                                                                  overflow:
                                                                      TextOverflow
                                                                          .ellipsis,
                                                                  style: GoogleFonts
                                                                      .roboto(
                                                                          color:
                                                                              AppColor.helpBlue)),
                                                              onTap: () {
                                                                controller
                                                                    .changeCoQuanLct(
                                                                        dsListCoQuanLCT);
                                                              },
                                                            ),
                                                            const Divider(
                                                                color: AppColor
                                                                    .greyColor),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        });
                                  },
                                  child: Container(
                                    color: AppColor.blueAccentColor,
                                    child: Padding(
                                      padding: const EdgeInsets.all(10),
                                      child: Obx(
                                        () => Text(
                                          controller
                                              .selectedCoQuan.value.tenDonVi
                                              .toString(),
                                          overflow: TextOverflow.ellipsis,
                                          style: GoogleFonts.roboto(
                                              color: Colors.white,
                                              fontSize: 20,
                                              fontStyle: FontStyle.italic),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: DropdownButtonFormField2<int>(
                                  isExpanded: true,
                                  decoration: const InputDecoration(
                                    contentPadding:
                                        EdgeInsets.symmetric(vertical: 10),
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1, color: Colors.white),
                                    ),
                                  ),
                                  hint: const Text(
                                    'Năm',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                  value: controller.selectYear.value,
                                  items: controller.years
                                      .map((item) => DropdownMenuItem<int>(
                                            value: item,
                                            child: Text(
                                              item.toString(),
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black),
                                            ),
                                          ))
                                      .toList(),
                                  validator: (value) {
                                    if (value == null) {
                                      return 'Please select gender.';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    controller.selectYear.value = value!;
                                    controller.loadDataInt();
                                    //Do something when selected item is changed.
                                  },
                                  onSaved: (value) {
                                    controller.selectYear.value = value!;
                                  },
                                  buttonStyleData: const ButtonStyleData(
                                    padding: EdgeInsets.only(right: 8),
                                  ),
                                  iconStyleData: const IconStyleData(
                                    icon: Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.black,
                                    ),
                                    iconSize: 24,
                                  ),
                                  dropdownStyleData: DropdownStyleData(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                  ),
                                  menuItemStyleData: MenuItemStyleData(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  controller.subMothds();
                                },
                                icon: Icon(Icons.arrow_back_ios_new,
                                    color: AppColor.blueAccentColor, size: 20)),
                            Obx(() => Text(
                                  'Tháng ${controller.moths.toString()}',
                                  style: GoogleFonts.roboto(
                                      color: AppColor.blueAccentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                )),
                            IconButton(
                                onPressed: () {
                                  controller.addMothds();
                                },
                                icon: Icon(Icons.arrow_forward_ios,
                                    color: AppColor.blueAccentColor, size: 20)),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Obx(
                        () => Wrap(
                          spacing: 5.0,
                          runSpacing: 5.0,
                          children: controller.listWeekByMoth.value
                              .map((item) => ItemTuan(
                                  tuan: item.tuan!.toInt().toString(),
                                  ngayBatDau: item.start,
                                  ngayKetThuc: item.end))
                              .toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 10, right: 10, top: 10),
                        child: Container(
                          width: Get.width,
                          color: AppColor.whiteColor,
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: InkWell(
                                  onTap: () {
                                    showModalBottomSheet(
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10))),
                                        context: context,
                                        builder: (BuildContext context) {
                                          return Container(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Text("Chọn đơn vị",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleSmall!
                                                          .copyWith(
                                                              color: AppColor
                                                                  .greyColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold)),
                                                ),
                                                const Divider(
                                                    color: AppColor.greyColor),
                                                Expanded(
                                                  child: ListView.builder(
                                                    itemCount: controller
                                                        .listDanhSachDonvi
                                                        .length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final dsListDonViLCT =
                                                          controller
                                                                  .listDanhSachDonvi[
                                                              index];
                                                      return Container(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            GestureDetector(
                                                              child: Text(
                                                                  "${dsListDonViLCT.tenDonVi}",
                                                                  style: GoogleFonts
                                                                      .roboto(
                                                                          color:
                                                                              AppColor.helpBlue)),
                                                              onTap: () {
                                                                controller
                                                                    .changeDonViLCt(
                                                                        dsListDonViLCT);
                                                              },
                                                            ),
                                                            const Divider(
                                                                color: AppColor
                                                                    .greyColor),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        });
                                  },
                                  child: Container(
                                    color: AppColor.blueAccentColor,
                                    child: Padding(
                                      padding: const EdgeInsets.all(10),
                                      child: Obx(
                                        () => Text(
                                          controller
                                              .selectedDonVi.value.tenDonVi
                                              .toString(),
                                          style: GoogleFonts.roboto(
                                              color: Colors.white,
                                              fontSize: 20,
                                              fontStyle: FontStyle.italic),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: DropdownButtonFormField2<int>(
                                  isExpanded: true,
                                  decoration: const InputDecoration(
                                    contentPadding:
                                        EdgeInsets.symmetric(vertical: 10),
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1, color: Colors.white),
                                    ),
                                  ),
                                  hint: const Text(
                                    'Năm',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                  value: controller.selectYear.value,
                                  items: controller.years
                                      .map((item) => DropdownMenuItem<int>(
                                            value: item,
                                            child: Text(
                                              item.toString(),
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black),
                                            ),
                                          ))
                                      .toList(),
                                  validator: (value) {
                                    if (value == null) {
                                      return 'Please select gender.';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    controller.selectYear.value = value!;
                                    controller.loadDataInt();
                                    //Do something when selected item is changed.
                                  },
                                  onSaved: (value) {
                                    controller.selectYear.value = value!;
                                  },
                                  buttonStyleData: const ButtonStyleData(
                                    padding: EdgeInsets.only(right: 8),
                                  ),
                                  iconStyleData: const IconStyleData(
                                    icon: Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.black,
                                    ),
                                    iconSize: 24,
                                  ),
                                  dropdownStyleData: DropdownStyleData(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                  ),
                                  menuItemStyleData: MenuItemStyleData(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  controller.subMothds();
                                },
                                icon: Icon(Icons.arrow_back_ios_new,
                                    color: AppColor.blueAccentColor, size: 20)),
                            Obx(() => Text(
                                  'Tháng ${controller.moths.toString()}',
                                  style: GoogleFonts.roboto(
                                      color: AppColor.blueAccentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                )),
                            IconButton(
                                onPressed: () {
                                  controller.addMothds();
                                },
                                icon: Icon(Icons.arrow_forward_ios,
                                    color: AppColor.blueAccentColor, size: 20)),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Obx(
                        () => Wrap(
                          spacing: 5.0,
                          runSpacing: 5.0,
                          children: controller.listWeekByMoth.value
                              .map((item) => ItemTuan(
                                  tuan: item.tuan!.toInt().toString(),
                                  ngayBatDau: item.start,
                                  ngayKetThuc: item.end))
                              .toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 10, right: 10, top: 10),
                        child: Container(
                          width: Get.width,
                          color: AppColor.whiteColor,
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: InkWell(
                                  onTap: () {
                                    showModalBottomSheet(
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(10),
                                                topRight: Radius.circular(10))),
                                        context: context,
                                        builder: (BuildContext context) {
                                          return Container(
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              children: <Widget>[
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Text("Chọn cán bộ",
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .titleSmall!
                                                          .copyWith(
                                                              color: AppColor
                                                                  .greyColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold)),
                                                ),
                                                const Divider(
                                                    color: AppColor.greyColor),
                                                Expanded(
                                                  child: ListView.builder(
                                                    itemCount: controller
                                                        .listDanhSachCanBo
                                                        .length,
                                                    itemBuilder:
                                                        (context, index) {
                                                      final dsListCanBoLCT =
                                                          controller
                                                                  .listDanhSachCanBo[
                                                              index];
                                                      return Container(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          children: [
                                                            GestureDetector(
                                                              child: Text(
                                                                  "${dsListCanBoLCT.hoVaTenCanBo}",
                                                                  style: GoogleFonts
                                                                      .roboto(
                                                                          color:
                                                                              AppColor.helpBlue)),
                                                              onTap: () {
                                                                controller
                                                                    .changeCaNhanLct(
                                                                        dsListCanBoLCT);
                                                              },
                                                            ),
                                                            const Divider(
                                                                color: AppColor
                                                                    .greyColor),
                                                          ],
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        });
                                  },
                                  child: Container(
                                    color: AppColor.blueAccentColor,
                                    child: Padding(
                                      padding: const EdgeInsets.all(10),
                                      child: Obx(
                                        () => Text(
                                          controller
                                              .selectedCaNhan.value.hoVaTenCanBo
                                              .toString(),
                                          style: GoogleFonts.roboto(
                                              color: Colors.white,
                                              fontSize: 20,
                                              fontStyle: FontStyle.italic),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: DropdownButtonFormField2<int>(
                                  isExpanded: true,
                                  decoration: const InputDecoration(
                                    contentPadding:
                                        EdgeInsets.symmetric(vertical: 10),
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(
                                          width: 1, color: Colors.white),
                                    ),
                                  ),
                                  hint: const Text(
                                    'Năm',
                                    style: TextStyle(fontSize: 12),
                                  ),
                                  value: controller.selectYear.value,
                                  items: controller.years
                                      .map((item) => DropdownMenuItem<int>(
                                            value: item,
                                            child: Text(
                                              item.toString(),
                                              style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.black),
                                            ),
                                          ))
                                      .toList(),
                                  validator: (value) {
                                    if (value == null) {
                                      return 'Please select gender.';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    controller.selectYear.value = value!;
                                    controller.loadDataInt();
                                    //Do something when selected item is changed.
                                  },
                                  onSaved: (value) {
                                    controller.selectYear.value = value!;
                                  },
                                  buttonStyleData: const ButtonStyleData(
                                    padding: EdgeInsets.only(right: 8),
                                  ),
                                  iconStyleData: const IconStyleData(
                                    icon: Icon(
                                      Icons.arrow_drop_down,
                                      color: Colors.black,
                                    ),
                                    iconSize: 24,
                                  ),
                                  dropdownStyleData: DropdownStyleData(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                  ),
                                  menuItemStyleData: MenuItemStyleData(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            IconButton(
                                onPressed: () {
                                  controller.subMothds();
                                },
                                icon: Icon(Icons.arrow_back_ios_new,
                                    color: AppColor.blueAccentColor, size: 20)),
                            Obx(() => Text(
                                  'Tháng ${controller.moths.toString()}',
                                  style: GoogleFonts.roboto(
                                      color: AppColor.blueAccentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                )),
                            IconButton(
                                onPressed: () {
                                  controller.addMothds();
                                },
                                icon: Icon(Icons.arrow_forward_ios,
                                    color: AppColor.blueAccentColor, size: 20)),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Obx(
                        () => Wrap(
                          spacing: 5.0,
                          runSpacing: 5.0,
                          children: controller.listWeekByMoth.value
                              .map((item) => ItemTuan(
                                  tuan: item.tuan!.toInt().toString(),
                                  ngayBatDau: item.start,
                                  ngayKetThuc: item.end))
                              .toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ))
        ],
      ),
    );
  }
}
