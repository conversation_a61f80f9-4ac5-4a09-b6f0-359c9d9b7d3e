import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/lichcongtac/lct_ld_detail_controller.dart';

class DetailLctLanhDao extends GetView<LctDetailLdController> {
  const DetailLctLanhDao({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      child: Container(
          child: controller.obx(
              (state) => Column(
                  children: controller.lctDetailThuLd
                      .map((e) => Padding(
                            padding: const EdgeInsets.only(
                                left: 5, right: 5, top: 5),
                            child: Container(
                              decoration: BoxDecoration(
                                  border: Border.all(
                                color: AppColor.blueAccentColor,
                              )),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Container(
                                      color: AppColor.blueAccentColor,
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Text(
                                              MethodUntils.getWeekdayName(
                                                      DateTime.parse(
                                                          e.ngayThucHien!)) ??
                                                  "",
                                              style: GoogleFonts.roboto(
                                                  color: Colors.white,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                            Text(
                                              e.data!.first.ngayThucHienVn! ??
                                                  "",
                                              style: GoogleFonts.roboto(
                                                  color: Colors.white,
                                                  fontSize: 13,
                                                  fontStyle: FontStyle.italic),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 3,
                                    child: Container(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: e.data!
                                            .map((details) => Column(
                                                  children: [
                                                    const SizedBox(
                                                      height: 5,
                                                    ),
                                                    Visibility(
                                                      visible: details.buoi!
                                                              .toInt() ==
                                                          1,
                                                      child: Divider(
                                                        height: 1,
                                                        color: AppColor
                                                            .blueAccentColor,
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 5,
                                                    ),
                                                    Row(
                                                      children: [
                                                        details.buoi!.toInt() ==
                                                                0
                                                            ? const Icon(
                                                                Icons.sunny,
                                                                color:
                                                                    Colors.red,
                                                                size: 30,
                                                              )
                                                            : const Icon(
                                                                Icons
                                                                    .nights_stay_outlined,
                                                                color: Color
                                                                    .fromARGB(
                                                                        255,
                                                                        6,
                                                                        69,
                                                                        141),
                                                                size: 30,
                                                              ),
                                                        Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Visibility(
                                                              visible:
                                                                  details.cb1 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb1 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            0)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb2 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb2 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            1)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb3 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb3 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            2)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb4 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb4 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            3)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb5 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb5 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            4)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb6 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb6 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            5)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb7 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb7 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            6)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb8 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb8 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            7)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb9 !=
                                                                      null,
                                                              child: Text(
                                                                details.cb9 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            8)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible: details
                                                                      .cb10 !=
                                                                  null,
                                                              child: Text(
                                                                details.cb10 !=
                                                                        null
                                                                    ? controller
                                                                        .getNameLanhDao(
                                                                            9)
                                                                    : "",
                                                                style: GoogleFonts.roboto(
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold),
                                                              ),
                                                            ),
                                                            // noi dung
                                                            Visibility(
                                                              visible:
                                                                  details.cb1 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb1
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb2 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb2
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb3 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb3
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb4 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb4
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb5 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb5
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb6 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb6
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb7 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb7
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb8 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb8
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                            Visibility(
                                                              visible:
                                                                  details.cb9 !=
                                                                      null,
                                                              child: Text(
                                                                MethodUntils
                                                                    .removeHTMLTag(
                                                                        details
                                                                            .cb9
                                                                            .toString()),
                                                                style: GoogleFonts
                                                                    .roboto(),
                                                              ),
                                                            ),
                                                          ],
                                                        )
                                                      ],
                                                    ),
                                                  ],
                                                ))
                                            .toList(),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ))
                      .toList()),
              onEmpty: Container(
                height: Get.height - 200,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Lottie.asset("lottie/emptyBox.json",
                          height: 200, width: 200),
                      const Text("Không tìm thấy dữ liệu!")
                    ],
                  ),
                ),
              ),
              onLoading: Container(
                height: Get.height - 200,
                child: Center(
                  child: SpinKitFadingCircle(
                    color: AppColor.blueAccentColor,
                  ),
                ),
              ))),
    ));
  }
}
