import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:vnpt_ioffice_camau/app/model/lct/model_lct_don_vi.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/lichcongtac/lich_cong_tac_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class ItemLdTuan extends StatelessWidget {
  final String tuan;
  final DateTime ngayBatDau;
  final DateTime ngayKetThuc;

  LichCongTacController lctController = Get.find();

  ItemLdTuan({
    super.key,
    required this.tuan,
    required this.ngayBatDau,
    required this.ngayKetThuc,
  });
  final dateFormat = DateFormat('dd/MM/yyyy');
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.toNamed(Routers.LCTDETAILANHDAO, arguments: {
          "tuan": tuan,
          "nam": lctController.selectYear.value,
          "maDonViQuanTri":
              lctController.selectedCoQuan.value.maDonViKc!.toInt()
        });
      },
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(
                color: (lctController.getWeekNow() == int.parse(tuan) &&
                        lctController.selectYear.value == DateTime.now().year)
                    ? Colors.red
                    : Colors.grey)),
        height: 190,
        width: 190,
        child: Column(
          children: [
            Container(
              color: (lctController.getWeekNow() == int.parse(tuan) &&
                      lctController.selectYear.value == DateTime.now().year)
                  ? Colors.red
                  : AppColor.blueAccentColor,
              width: Get.width,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "Tuần",
                  textAlign: TextAlign.center,
                  style: GoogleFonts.roboto(
                      fontSize: 15,
                      color: Colors.white,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  child: Text(
                    tuan,
                    style:
                        GoogleFonts.roboto(color: Colors.black, fontSize: 50),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5, right: 5),
                  child: Text(
                    "Thời gian",
                    style: GoogleFonts.roboto(
                        color: Colors.grey,
                        fontSize: 13,
                        fontStyle: FontStyle.italic),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 5, right: 5, bottom: 10),
                  child: Text(
                    "${dateFormat.format(ngayBatDau)} - ${dateFormat.format(ngayKetThuc)}",
                    style: GoogleFonts.roboto(
                        color: Colors.grey,
                        fontSize: 13,
                        fontStyle: FontStyle.italic),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
