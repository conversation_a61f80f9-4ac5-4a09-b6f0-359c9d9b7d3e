class GetStorageKey {
  static const isDarkMode = "isDarkMode";
  static const domainApi = "domain_api";
  static const userName = "iOusername";
  static const passWord = "iOpassword";
  static const isLogin = "isLogin";
  // thông tin cán bộ
  static const maCtcbKc = "ma_ctcb_kc";
  static const chucVu = "chuc_vu";
  static const hoVaTen = "ho_va_ten";
  static const isVanThu = "van_thu";
  static const isLanhDao = "lanh_dao";
  static const isChuyenVien = "chuyen_vien";
  static const diDongCanBo = "di_dong_can_bo";
  static const maCanBo = "ma_can_bo";
  static const refreshToken = "refresh_token";
  static const loaiKySoSim = "loai_ky_so_sim";
  static const expiredTimeToken = 'expiredTime';
  // thông tin đơn vị và đomain file
  static const maDonVi = "ma_don_vi";
  static const maDonViQuanTri = "ma_don_vi_quan_tri";
  static const tenDonViQuanTri = "ten_don_vi_quan_tri";
  static const maDinhDanhQuanTri = "ma_dinh_danh_quan_tri";
  static const domainFile = "domain_file";
  static const filePartition = "file_partition";
  // tham số
  static const tsHtChonTgianNhacViec = "ht_chon_tgian_nhac_viec";
  static const tsTnMauGuiSms = "tn_mau_gui_sms";
  static const tslocDauTinNhan = "loc_dau_tin_nhan";
  static const tsChuVuCbTinNhan = "chu_vu_cb_tin_nhan";
  static const tsTienToTinNhan = "tien_to_tin_nhan";
  static const vbdeGopChungVbDienTu = "vbde_gop_chung_vb_dien_tu";
  // thong tin edoc
  static const userEdoc = "user_edoc";
  static const passEdoc = "pass_edoc";
  static const pubKeyEdoc = "pub_key_edoc";
  static const priKeyEdoc = "pri_key_edoc";
  // thông tin smartCa
  static const smartCaAccessToken = "smartca_access_token";
  static const smartCaExpire = "smartca_expire";

  // cconffig ký số sim
  static const usMobilePkiMode = "US_Mobile_Pki_Mode";
  static const usMobilePkiSide = "US_Mobile_Pki_Side";
  static const usMobilePkiWidthRec = "US_Mobile_Pki_Width_Rec";
  static const usMobilePkiHeightRec = "US_Mobile_Pki_Height_Rec";
  static const cksXoaFileSauKhiKySo = "cks_xoa_file_sau_khi_ky_so";

  // ký số smartCA
  static const cksSmartCaUrl = "cks_smartca_url";
  static const cksSmartCaClientId = "cks_smartca_client_id";
  static const cksSmartCaCallback = "cks_smartca_callback";
  // token FCM
  static const accessTokenFCM = "token_fcm";
  static const maCtcbKcStore = "ma_ctcb_kc_store";
  // id Token SSO
  static const idTokenSSO = "id_token_sso";
  static const domainSSO = "domain_sso";
  static const dateTimeLogin = "datetime_login";
  // xem văn ban đến
  static const maVanBanDenKc = "ma_van_ban_den_kc";
  static const maXulyDenKc = "ma_xu_ky_den_kc";
  static const trichYeu = "trich_yeu";
  static const maYeuCau = "ma_yeu_cau";
// xem văn bản đi
  static const maVanBanDiKc = "ma_van_ban_di_kc";
  static const maXuLyDiKc = "ma_xu_ly_di_kc";
  static const fileVbdi = "file_vbdi";
  static const maXuLyDiCha = "ma_xu_ly_di_cha";
  static const trichYeuDi = "trich_yeu_di";

  // tham số chuyền vào ký số
  static const maCongVan = "ma_cong_van";
  static const maXuLyCongVan = "ma_xu_ly_cong_van";
  static const srcFileKySo = "src_file_ky_so";
  static const isLoaiKySo = "is_loai_ky_so";
  static const isVbnbKySo = "is_vbnb_ky_so";
  static const pathFileKySo = "path_file_ky_so";
  static const isKsPhieuTrinh = "is_ks_phieu_trinh";
  static const filesPhieuTrinh = "files_phieu_trinh";

  // tham số lấy hạn xử lý văn bản đến
  static const vbdeXuLySoNgayHxl = "vbde_xuly_so_ngay_hxl";
  static const vbdeUserHienThiSoNgayHxl = "vbde_user_hienthi_so_ngay_hxl";
}
