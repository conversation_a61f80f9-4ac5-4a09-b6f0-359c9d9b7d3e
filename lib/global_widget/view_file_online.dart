import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:vnpt_ioffice_camau/core/utils/method_utils.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

class ModalViewFileOnline {
  static void ViewFileOnline({
    required String tenFile,
    required String path,
    required String? item,
  }) async {
    final _store = await GetStorage();
    var domainFile = _store.read(GetStorageKey.domainFile);
    var domainApi = _store.read(GetStorageKey.domainApi);
    String extentionFile = tenFile.split(".").removeLast().toLowerCase();
    SetupController setupController = Get.find();
    late String downloadNoLogin;
    var url = '';
    if (extentionFile == 'pdf') {
      url = '${MethodUntils.getViewFileDomain(domainApi)}$path';
    } else {
      downloadNoLogin =
          '$domainFile/api/file-manage/read-file-not-login/$item/view/view-documen';
      url =
          'https://view.officeapps.live.com/op/embed.aspx?src=${downloadNoLogin}'; //'${MethodUntils.getViewFileDomain(domainApi)}$path';
    }

    var res =
        base64UrlEncode(utf8.encode(_store.read(GetStorageKey.refreshToken)));
    Get.dialog(Scaffold(
      appBar: AppBar(
          title: Text(
            tenFile ?? '',
            style: const TextStyle(color: Colors.white),
            overflow: TextOverflow.ellipsis,
          ),
          actions: [
            Row(
              children: [
                Visibility(
                  visible: GetPlatform.isAndroid,
                  child: IconButton(
                    onPressed: () {
                      setupController.downloadFileDivice(path, tenFile);
                    },
                    icon: const Icon(Icons.download),
                  ),
                ),
                const SizedBox(
                  width: 5,
                ),
                IconButton(
                    onPressed: () {
                      setupController.shareFile(path, tenFile);
                    },
                    icon: const Icon(Icons.share))
              ],
            )
          ],
          backgroundColor: AppColor.blueAccentColor,
          foregroundColor: AppColor.whiteColor),
      body: Column(
        children: [
          Expanded(
            child: WebViewWidget(
                controller: WebViewController()
                  ..setJavaScriptMode(JavaScriptMode.disabled)
                  ..setJavaScriptMode(JavaScriptMode.unrestricted)
                  ..loadRequest(Uri.parse(url))),
          ),
        ],
      ),
    ));
  }
}
