import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:vnpt_ioffice_camau/core/values/app_lottie.dart';

class EmptyFailureNoInternetView extends StatelessWidget {
  final String description;
  const EmptyFailureNoInternetView({required this.description, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 10, right: 10),
      child: SingleChildScrollView(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Lottie.asset(
                AppLottie.notFound,
                height: 400,
              ),
              const SizedBox(
                height: 10,
              ),
              Text(description)
            ],
          ),
        ),
      ),
    );
  }
}
