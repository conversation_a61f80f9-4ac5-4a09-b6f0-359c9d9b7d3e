import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/global_widget/empty_failure_no_network.dart';
import 'package:vnpt_ioffice_camau/global_widget/navigation_bottom.dart'
    as navigateBottom;
import 'package:vnpt_ioffice_camau/global_widget/navigation_drawer.dart'
    as navigaDrawer;
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';

class HeaderAppbar extends StatelessWidget {
  final SetupController commonController = Get.find();
  final Widget body;
  final String title;
  final bool isDrawer;
  final bool? isConfirm;
  final String pathScreen;
  HeaderAppbar(
      {super.key,
      required this.body,
      required this.title,
      required this.isDrawer,
      required this.pathScreen,
      this.isConfirm});

  @override
  Widget build(BuildContext context) {
    final HomeController homeController = Get.find();
    return Scaffold(
        appBar: AppBar(
          title: Text(
            title,
            style: GoogleFonts.roboto(color: Colors.white),
          ),
          backgroundColor: AppColor.blueAccentColor,
          foregroundColor: Colors.white,
          leading: isDrawer
              ? null
              : IconButton(
                  icon: const Icon(Icons.arrow_back),
                  color: Colors.white,
                  onPressed: () {
                    commonController.isCheckButtonBack(pathScreen);
                    if (isConfirm != true) homeController.loadDSnv();
                    // Điều hướng trở lại màn hình trước đó
                  },
                ),
          actions: isConfirm == true
              ? [
                  IconButton(
                      onPressed: () => commonController.isCheckXuLy(pathScreen),
                      icon: commonController.showIconAppBar(pathScreen))
                ]
              : null,
        ),
        drawer: navigaDrawer.NavigationDrawer(),
        bottomNavigationBar: navigateBottom.NavigationBottom(),
        body: commonController.networkController.connectionStatus.value == 1
            ? body
            : EmptyFailureNoInternetView(description: AppString.noInternet));
  }
}
