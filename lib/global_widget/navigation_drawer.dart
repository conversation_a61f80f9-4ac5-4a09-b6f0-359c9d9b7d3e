import 'dart:ffi';

import 'package:badges/badges.dart' as bage;
import 'package:badges/badges.dart';
import 'package:circular_profile_avatar/circular_profile_avatar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:vnpt_ioffice_camau/core/utils/custom_snack_bar.dart';
import 'package:vnpt_ioffice_camau/core/values/get_storage_key.dart';
import 'package:vnpt_ioffice_camau/core/values/app_color.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/common/setup_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/login/login_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/mauchuky/mauchuky_controller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/danh_ba_controlller.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/dscv_vbdi_controller.dart';
import 'package:vnpt_ioffice_camau/routers/app_pages.dart';

class NavigationDrawer extends StatelessWidget {
  final getStorage = GetStorage();
  final SetupController setupController = Get.find();
  HomeController homeController = Get.find();
  String? initialsUser = "";
  String urlImageUser =
      "https://ioffice.camau.gov.vn/dist/img/avatar_default.png";
  String accountPosition = "";
  @override
  Widget build(BuildContext context) {
    return Drawer(
        backgroundColor: Colors.white,
        child: Column(children: [
          buidHearderDrawer(context,
              urlImageUser: urlImageUser,
              accountName: getStorage.read(GetStorageKey.hoVaTen) ?? '',
              accountPosition: getStorage.read(GetStorageKey.chucVu) ?? ''),
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Theme(
                data: ThemeData(
                  dividerColor: Colors.transparent,
                ),
                child: Column(children: [
                  buildDrawerItems(
                      text: 'Trang chủ',
                      icon: Icons.home,
                      isChild: false,
                      numberNv: null,
                      textIconColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.helpBlue
                              : AppColor.helpBlue)
                          : (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.helpBlue),
                      titleColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.whiteColor)
                          : null,
                      onTap: () => navigate('home')),
                  ExpansionTile(
                    backgroundColor: Colors.white,
                    collapsedBackgroundColor: Colors.white,
                    leading: const Icon(
                      Icons.description_outlined,
                      color: AppColor.helpBlue,
                    ),
                    title: Text(
                      'Văn bản đến',
                      style: GoogleFonts.roboto(color: AppColor.helpBlue),
                    ),
                    children: [
                      (getStorage.read(GetStorageKey.isVanThu) == 1)
                          ? buildDrawerItems(
                              text: 'Sổ văn bản đến',
                              icon: Icons.description,
                              isChild: true,
                              numberNv: homeController.nvSoVbde.value,
                              textIconColor: Get.currentRoute == Routers.HOME
                                  ? (Get.isDarkMode
                                      ? AppColor.helpBlue
                                      : AppColor.helpBlue)
                                  : (Get.isDarkMode
                                      ? AppColor.whiteColor
                                      : AppColor.helpBlue),
                              titleColor: Get.currentRoute == Routers.HOME
                                  ? (Get.isDarkMode
                                      ? AppColor.whiteColor
                                      : AppColor.whiteColor)
                                  : null,
                              onTap: () => navigate('vbde_so'))
                          : const SizedBox(),
                      (getStorage.read(GetStorageKey.isLanhDao) == 1)
                          ? buildDrawerItems(
                              text: 'Duyệt',
                              icon: Icons.description,
                              isChild: true,
                              numberNv: homeController.nvChoDuyet.value,
                              textIconColor: Get.currentRoute == Routers.HOME
                                  ? (Get.isDarkMode
                                      ? AppColor.helpBlue
                                      : AppColor.helpBlue)
                                  : (Get.isDarkMode
                                      ? AppColor.whiteColor
                                      : AppColor.helpBlue),
                              titleColor: Get.currentRoute == Routers.HOME
                                  ? (Get.isDarkMode
                                      ? AppColor.whiteColor
                                      : AppColor.whiteColor)
                                  : null,
                              onTap: () => navigate('vbde_duyet'))
                          : SizedBox(),
                      buildDrawerItems(
                          text: 'Xử lý',
                          icon: Icons.description,
                          isChild: true,
                          numberNv: homeController.nvVbdeXuly.value,
                          textIconColor: Get.currentRoute == Routers.HOME
                              ? (Get.isDarkMode
                                  ? AppColor.helpBlue
                                  : AppColor.helpBlue)
                              : (Get.isDarkMode
                                  ? AppColor.whiteColor
                                  : AppColor.helpBlue),
                          titleColor: Get.currentRoute == Routers.HOME
                              ? (Get.isDarkMode
                                  ? AppColor.whiteColor
                                  : AppColor.whiteColor)
                              : null,
                          onTap: () => navigate('vbde_xuly')),
                    ],
                  ),
                  ExpansionTile(
                    backgroundColor: Colors.white,
                    collapsedBackgroundColor: Colors.white,
                    leading: const Icon(Icons.description_outlined,
                        color: AppColor.helpBlue),
                    title: Text("Văn bản đi",
                        style: GoogleFonts.roboto(color: AppColor.helpBlue)),
                    children: [
                      (getStorage.read(GetStorageKey.isLanhDao) == 1)
                          ? buildDrawerItems(
                              text: 'Duyệt',
                              icon: Icons.description,
                              isChild: true,
                              numberNv: homeController.nvVbdiChoDuyet.value,
                              textIconColor: Get.currentRoute == Routers.HOME
                                  ? (Get.isDarkMode
                                      ? AppColor.helpBlue
                                      : AppColor.helpBlue)
                                  : (Get.isDarkMode
                                      ? AppColor.whiteColor
                                      : AppColor.helpBlue),
                              titleColor: Get.currentRoute == Routers.HOME
                                  ? (Get.isDarkMode
                                      ? AppColor.whiteColor
                                      : AppColor.whiteColor)
                                  : null,
                              onTap: () => navigate('vbdi_duyet'))
                          : const Text(''),
                      buildDrawerItems(
                          text: 'Xử lý',
                          icon: Icons.description,
                          isChild: true,
                          numberNv: homeController.nvVbdiXuLy.value,
                          textIconColor: Get.currentRoute == Routers.HOME
                              ? (Get.isDarkMode
                                  ? AppColor.helpBlue
                                  : AppColor.helpBlue)
                              : (Get.isDarkMode
                                  ? AppColor.whiteColor
                                  : AppColor.helpBlue),
                          titleColor: Get.currentRoute == Routers.HOME
                              ? (Get.isDarkMode
                                  ? AppColor.whiteColor
                                  : AppColor.whiteColor)
                              : null,
                          onTap: () => navigate('vbdi_xuly')),
                      buildDrawerItems(
                          text: 'Đã phát hành',
                          icon: Icons.description,
                          isChild: true,
                          numberNv: 0,
                          textIconColor: Get.currentRoute == Routers.HOME
                              ? (Get.isDarkMode
                                  ? AppColor.helpBlue
                                  : AppColor.helpBlue)
                              : (Get.isDarkMode
                                  ? AppColor.whiteColor
                                  : AppColor.helpBlue),
                          titleColor: Get.currentRoute == Routers.HOME
                              ? (Get.isDarkMode
                                  ? AppColor.whiteColor
                                  : AppColor.whiteColor)
                              : null,
                          onTap: () => navigate('da_phat_hanh')),
                    ],
                  ),
                  ExpansionTile(
                      backgroundColor: Colors.white,
                      collapsedBackgroundColor: Colors.white,
                      leading: const Icon(Icons.manage_search,
                          color: AppColor.helpBlue),
                      title: const Text("Tra cứu văn bản",
                          style: TextStyle(color: AppColor.helpBlue)),
                      children: [
                        buildDrawerItems(
                            text: 'Văn bản đến',
                            icon: Icons.keyboard_double_arrow_right,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('search_vbde');
                            }),
                        buildDrawerItems(
                            text: 'Văn bản đi',
                            icon: Icons.keyboard_double_arrow_left,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('search_vbdi');
                            })
                      ]),
                  buildDrawerItems(
                      text: 'Thông tin điều hành',
                      icon: Icons.checklist_outlined,
                      isChild: false,
                      numberNv: null,
                      textIconColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.helpBlue
                              : AppColor.helpBlue)
                          : (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.helpBlue),
                      titleColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.whiteColor)
                          : null,
                      onTap: () {
                        navigate('ttdh');
                      }),
                  buildDrawerItems(
                      text: 'Văn bản nội bộ',
                      icon: Icons.sync_alt_outlined,
                      isChild: false,
                      numberNv: homeController.nvVbNoiBo.value,
                      textIconColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.helpBlue
                              : AppColor.helpBlue)
                          : (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.helpBlue),
                      titleColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.whiteColor)
                          : null,
                      onTap: () {
                        navigate('vbnb_xuly');
                      }),
                  buildDrawerItems(
                      text: ' Lịch công tác',
                      icon: Icons.calendar_month,
                      isChild: false,
                      numberNv: homeController.nvVbNoiBo.value,
                      textIconColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.helpBlue
                              : AppColor.helpBlue)
                          : (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.helpBlue),
                      titleColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.whiteColor)
                          : null,
                      onTap: () {
                        navigate('lich_cong_tac');
                      }),
                  ExpansionTile(
                      backgroundColor: Colors.white,
                      collapsedBackgroundColor: Colors.white,
                      leading: const Icon(Icons.person_outline,
                          color: AppColor.helpBlue),
                      title: Text("Cá nhân",
                          style: GoogleFonts.roboto(color: AppColor.helpBlue)),
                      children: [
                        buildDrawerItems(
                            text: 'Thông tin cá nhân',
                            icon: Icons.manage_accounts,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('ttcanhan');
                            }),
                        buildDrawerItems(
                            text: 'Danh bạ',
                            icon: Icons.contact_page,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('danh_ba');
                            }),
                        buildDrawerItems(
                            text: 'Mẫu chữ ký',
                            icon: Icons.draw,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('mauchuky');
                            }),
                      ]),
                  ExpansionTile(
                      backgroundColor: Colors.white,
                      collapsedBackgroundColor: Colors.white,
                      leading: const Icon(Icons.support_agent,
                          color: AppColor.helpBlue),
                      title: Text("Hỗ trợ",
                          style: GoogleFonts.roboto(color: AppColor.helpBlue)),
                      children: [
                        buildDrawerItems(
                            text: 'Thu hồi văn bản',
                            icon: Icons.restore_page,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('hotro_nguoidung');
                            }),
                        buildDrawerItems(
                            text: 'Reset mật khẩu',
                            icon: Icons.lock_reset,
                            isChild: true,
                            numberNv: null,
                            textIconColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.helpBlue
                                    : AppColor.helpBlue)
                                : (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.helpBlue),
                            titleColor: Get.currentRoute == Routers.HOME
                                ? (Get.isDarkMode
                                    ? AppColor.whiteColor
                                    : AppColor.whiteColor)
                                : null,
                            onTap: () {
                              navigate('reset_mknguoidung');
                            }),
                      ]),
                  buildDrawerItems(
                      text: 'Đăng xuất',
                      icon: Icons.logout,
                      isChild: false,
                      numberNv: null,
                      textIconColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.helpBlue
                              : AppColor.helpBlue)
                          : (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.helpBlue),
                      titleColor: Get.currentRoute == Routers.HOME
                          ? (Get.isDarkMode
                              ? AppColor.whiteColor
                              : AppColor.whiteColor)
                          : null,
                      onTap: () {
                        setupController.logOut();
                      }),
                ]),
              ),
            ),
          ),
        ]));
  }

  Widget buidHearderDrawer(BuildContext context,
      {required String urlImageUser,
      required String accountName,
      required String accountPosition}) {
    List<DropdownMenuItem<String>> listDropDownChucVu = [];

    homeController.dsChucVuKiemNhiem.value.forEach((element) {
      listDropDownChucVu.add(DropdownMenuItem(
          child: Text(
            element.tenChucVu!.toString(),
            style: GoogleFonts.roboto(),
          ),
          value: element.maCtcbKc!.toInt().toString()));
    });
    return DrawerHeader(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? AppColor.yellowColor : AppColor.blueAccentColor,
      ),
      child: Row(children: [
        const CircleAvatar(
          radius: 30.0,
          backgroundImage: AssetImage("images/avatar_default.png"),
          backgroundColor: Colors.transparent,
        ),
        Expanded(
          flex: 1,
          child: Padding(
            padding: const EdgeInsets.only(left: 20),
            child:
                Column(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text(accountName,
                  style: GoogleFonts.roboto(
                      color: Get.isDarkMode
                          ? AppColor.blackColor
                          : AppColor.whiteColor,
                      fontWeight: FontWeight.bold)),
              const SizedBox(
                height: 10,
              ),
              Visibility(
                visible: listDropDownChucVu.length == 1,
                child: Text(
                  accountPosition,
                  style: GoogleFonts.roboto(
                      color: Get.isDarkMode
                          ? AppColor.blackColor
                          : AppColor.whiteColor,
                      fontWeight: FontWeight.bold),
                ),
              ),
              Visibility(
                visible: listDropDownChucVu.length > 1,
                child: Obx(() => DropdownButton(
                    items: listDropDownChucVu,
                    value: homeController.selectChucVu.value,
                    iconEnabledColor: Colors.white,
                    dropdownColor: AppColor.blueAccentColor,
                    style: Theme.of(context).textTheme.titleSmall!.copyWith(
                        color: Get.isDarkMode
                            ? AppColor.blackColor
                            : AppColor.whiteColor,
                        fontWeight: FontWeight.bold),
                    onChanged: ((value) {
                      homeController.changeMaCtbKiemNhiem(value!);
                    }))),
              ),
            ]),
          ),
        )
      ]),
    );
  }

  Widget buildDrawerItems(
      {required String text,
      required IconData? icon,
      required bool isChild,
      required Color? textIconColor,
      required int? numberNv,
      required Color? titleColor,
      required VoidCallback onTap}) {
    return isChild
        ? Padding(
            padding: const EdgeInsets.only(left: 20),
            child: ListTile(
              leading: Icon(icon, color: textIconColor),
              title: Text(
                text,
                style: GoogleFonts.roboto(color: textIconColor),
              ),
              tileColor: titleColor,
              onTap: onTap,
              trailing: (numberNv != 0 && numberNv != null)
                  ? bage.Badge(
                      badgeStyle: const BadgeStyle(
                        badgeColor: Colors.red,
                      ),
                      position: BadgePosition.topEnd(top: -20, end: -12),
                      badgeContent: Text(
                        numberNv.toString(),
                        style: GoogleFonts.roboto(color: Colors.white),
                      ))
                  : null,
            ),
          )
        : ListTile(
            leading: Icon(icon, color: textIconColor),
            title: Text(
              text,
              style: GoogleFonts.roboto(color: textIconColor),
            ),
            trailing: (numberNv != 0 && numberNv != null)
                ? bage.Badge(
                    badgeStyle: const BadgeStyle(
                      badgeColor: Colors.red,
                    ),
                    position: BadgePosition.topEnd(top: -20, end: -12),
                    badgeContent: Text(
                      numberNv.toString(),
                      style: GoogleFonts.roboto(color: Colors.white),
                    ))
                : null,
            tileColor: titleColor,
            onTap: onTap,
          );
  }

  navigate(String index) {
    switch (index) {
      case 'home':
        Get.offAndToNamed(Routers.HOME);
        break;
      case 'vbde_so':
        Get.offNamed(Routers.SOVBDENVT);
        break;
      case 'vbde_duyet': // duỵet vbdem
        Get.offNamed(Routers.VBDEN);
        break;
      case 'vbde_xuly':
        Get.offNamed(Routers.XULYCVVBDE);
        break;
      case 'vbdi_duyet':
        Get.offNamed(Routers.DUYETVBDI);
        break;
      case 'vbdi_xuly':
        Get.offNamed(Routers.DSCVVBDI, arguments: {"index": 0});
        break;
      case 'vbnb_xuly':
        Get.offNamed(Routers.VBNB);
        break;
      case 'ttdh':
        Get.offNamed(Routers.TTDH);
        break;
      case 'search_vbdi':
        Get.offNamed(Routers.TRACUUVBDI);
        break;
      case 'search_vbde':
        Get.offNamed(Routers.TRACUUVBDE);
        break;
      case 'lich_cong_tac':
        Get.offNamed(Routers.LICHCONGTAC);
        break;
      case 'ttcanhan':
        Get.offNamed(Routers.TTCANHAN);
        break;
      case 'mauchuky':
        Get.offNamed(Routers.MAUCHUKY);
        break;
      case 'danh_ba':
        Get.offNamed(Routers.DANHBA);
        break;
      case 'da_phat_hanh':
        Get.offAllNamed(Routers.DSCVVBDI, arguments: {"index": 3});
        break;
      case 'hotro_nguoidung':
        Get.offNamed(Routers.HOTRONGUOIDUNG);
        break;
      case 'reset_mknguoidung':
        Get.offNamed(Routers.RESETMATKHAUNGUOIDUNG);
        break;
      default:
        break;
    }
  }
}
