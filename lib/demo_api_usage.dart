import 'package:vnpt_ioffice_camau/app/model/hotro/jsTree_model.dart';

void main() {
  // Demo sử dụng UnitTreeNode với API
  demoApiUsage();
}

void demoApiUsage() {
  print('=== Demo UnitTreeNode API Usage ===\n');

  // 1. Tạo UnitTreeNode từ JSON (từ API response)
  print('1. Tạo UnitTreeNode từ JSON:');
  final jsonFromApi = {
    'id': '1',
    'text': 'Phòng Công nghệ thông tin',
    'parent': '0',
    'state': {
      'opened': true,
      'selected': false,
    }
  };

  final unitFromJson = UnitTreeNode.fromJsTree(jsonFromApi);
  print('   - ID: ${unitFromJson.id}');
  print('   - Name: ${unitFromJson.name}');
  print('   - Parent ID: ${unitFromJson.parentId}');
  print('   - Expanded: ${unitFromJson.expanded.value}');
  print('   - Selected: ${unitFromJson.selected.value}\n');

  // 2. Chuyển UnitTreeNode thành JSON (để gửi lên API)
  print('2. Chuyển UnitTreeNode thành JSON:');
  final unitToSend = UnitTreeNode(
    id: 2,
    name: 'Phòng Hành chính - Nhân sự',
    parentId: 0,
    expanded: true,
    selected: false,
  );

  final jsonToSend = unitToSend.toJsTree();
  print('   JSON to send: $jsonToSend\n');

  // 3. Demo với cấu trúc cây phức tạp
  print('3. Demo cấu trúc cây phức tạp:');
  final complexTree = UnitTreeNode(
    id: 0,
    name: 'VNPT Cà Mau',
    expanded: true,
    children: [
      UnitTreeNode(
        id: 1,
        name: 'Ban Giám đốc',
        parentId: 0,
        children: [
          UnitTreeNode(id: 11, name: 'Giám đốc', parentId: 1),
          UnitTreeNode(id: 12, name: 'Phó Giám đốc', parentId: 1),
        ],
      ),
      UnitTreeNode(
        id: 2,
        name: 'Phòng Công nghệ thông tin',
        parentId: 0,
        children: [
          UnitTreeNode(id: 21, name: 'Bộ phận Phát triển phần mềm', parentId: 2),
          UnitTreeNode(id: 22, name: 'Bộ phận Hạ tầng mạng', parentId: 2),
        ],
      ),
    ],
  );

  final complexJson = complexTree.toJson();
  print('   Complex tree JSON:');
  print('   ${complexJson.toString()}\n');

  // 4. Demo fromJson với cấu trúc phức tạp
  print('4. Tạo lại cây từ JSON:');
  final recreatedTree = UnitTreeNode.fromJson(complexJson);
  print('   - Root name: ${recreatedTree.name}');
  print('   - Children count: ${recreatedTree.children.length}');
  print('   - First child: ${recreatedTree.children.first.name}');
  print('   - First child\'s children: ${recreatedTree.children.first.children.length}\n');

  // 5. Demo copyWith
  print('5. Demo copyWith:');
  final originalUnit = UnitTreeNode(
    id: 1,
    name: 'Original Name',
    expanded: false,
  );

  final copiedUnit = originalUnit.copyWith(
    name: 'Updated Name',
    expanded: true,
  );

  print('   - Original: ${originalUnit.name}, expanded: ${originalUnit.expanded.value}');
  print('   - Copied: ${copiedUnit.name}, expanded: ${copiedUnit.expanded.value}\n');

  print('=== Demo completed ===');
}

// Ví dụ về cách sử dụng trong API service
class UnitApiService {
  // Lấy danh sách đơn vị từ API
  Future<List<UnitTreeNode>> getUnitsFromApi() async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Giả lập response từ API
      final apiResponse = {
        "success": true,
        "data": [
          {
            "id": "1",
            "text": "Phòng Công nghệ thông tin",
            "parent": "#",
            "state": {"opened": true, "selected": false}
          },
          {
            "id": "2",
            "text": "Bộ phận Phát triển phần mềm",
            "parent": "1",
            "state": {"opened": false, "selected": false}
          }
        ]
      };

      if (apiResponse['success'] == true) {
        final dataList = apiResponse['data'] as List;
        return dataList.map((json) => UnitTreeNode.fromJsTree(json)).toList();
      }
      
      return [];
    } catch (e) {
      print('Error fetching units: $e');
      return [];
    }
  }

  // Gửi đơn vị lên API
  Future<bool> saveUnitToApi(UnitTreeNode unit) async {
    try {
      final jsonData = unit.toJsTree();
      
      // Simulate API call
      print('Sending to API: $jsonData');
      await Future.delayed(const Duration(seconds: 1));
      
      return true;
    } catch (e) {
      print('Error saving unit: $e');
      return false;
    }
  }

  // Cập nhật đơn vị
  Future<bool> updateUnit(UnitTreeNode unit) async {
    try {
      final jsonData = unit.toJson();
      
      // Simulate API call
      print('Updating unit: $jsonData');
      await Future.delayed(const Duration(seconds: 1));
      
      return true;
    } catch (e) {
      print('Error updating unit: $e');
      return false;
    }
  }
}
