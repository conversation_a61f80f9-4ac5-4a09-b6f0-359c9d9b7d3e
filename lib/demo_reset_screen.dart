import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/resetmk_controller.dart';
import 'package:vnpt_ioffice_camau/modules/views/hotro/resetmk_screen.dart';

class DemoResetScreen extends StatelessWidget {
  const DemoResetScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize controller
    Get.put(ResetMkNguoiDungController());
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Demo Reset Mật Khẩu'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const ResetMatKhauNguoiDung(),
    );
  }
}
