import 'package:get/get.dart';

import 'package:vnpt_ioffice_camau/core/values/app_string.dart';
import 'package:vnpt_ioffice_camau/global_widget/header_appbar.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/controllers/hotro/hotro_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/lichcongtac/lct_ld_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/lichcongtac/lichcongtac_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/login/login_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/mauchuky/mauchuky_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/tracuu/tracuu_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/ttdh/ttdh_bindings.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/user/user_binding.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbde/vbde_bindings.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbdi/vbdi_bindings.dart';
import 'package:vnpt_ioffice_camau/modules/controllers/vbnb/vbnb_bindings.dart';
import 'package:vnpt_ioffice_camau/modules/views/home/<USER>';
import 'package:vnpt_ioffice_camau/modules/views/hotro/hotro_screen.dart';
import 'package:vnpt_ioffice_camau/modules/views/hotro/resetmk_screen.dart';
import 'package:vnpt_ioffice_camau/modules/views/lichcongtac/detail_lct_lanh_dao.dart';
import 'package:vnpt_ioffice_camau/modules/views/lichcongtac/detail_thu.dart';
import 'package:vnpt_ioffice_camau/modules/views/lichcongtac/lich_cong_tac_view.dart';
import 'package:vnpt_ioffice_camau/modules/views/login/login_screen.dart';
import 'package:vnpt_ioffice_camau/modules/views/tracuu/tracuu_detail_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/tracuu/tracuu_detail_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/tracuu/tracuu_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/tracuu/tracuu_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/ttdh/thongtindieuhanh.dart';
import 'package:vnpt_ioffice_camau/modules/views/ttdh/ttdh_detail.dart';
import 'package:vnpt_ioffice_camau/modules/views/ttdh/ttdh_phanhoi.dart';
import 'package:vnpt_ioffice_camau/modules/views/user/danh_ba.dart';
import 'package:vnpt_ioffice_camau/modules/views/user/mau_chu_ky.dart';
import 'package:vnpt_ioffice_camau/modules/views/user/thong_tin_ca_nhan.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/chuyenxuly/chuyen_cv_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/chuyenxuly/chuyen_ld_khac.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/chuyenxuly/duyet_xuly_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/chuyenxuly/hoan_tat_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/chuyenxuly/nhapykien_cv_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/detail_item_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/duyetld_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/sovanbanvt_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/them_moi_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/theodoi_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbde/xulycv_vbde.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/chuyenxuly/chuyen_cv_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/chuyenxuly/chuyen_ld_khac_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/chuyenxuly/chuyen_vt_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/chuyenxuly/cvchuyen_cv_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/chuyenxuly/cvchuyen_ld_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/chuyenxuly/cvchuyen_vt_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/detail_item_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/dsxuly_cv_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/duyetld_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbdi/view_ca_vbdi.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbnb/detail_item_vbnb.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbnb/vbnb.dart';
import 'package:vnpt_ioffice_camau/modules/views/vbnb/vbnb_chuyen.dart';

part 'app_routers.dart';

class AppPages {
  AppPages._();
  static const INITIAL = Routers.LOGIN;

  static final routers = [
    GetPage(name: _Paths.LOGIN, page: () => Login(), binding: LoginBinding()),
    GetPage(
        name: _Paths.HOME,
        page: () => HeaderAppbar(
            body: Home(),
            title: AppString.homeTitle,
            isDrawer: true,
            pathScreen: _Paths.HOME),
        binding: HomeBinding()),
    GetPage(
        name: _Paths.VBDEN,
        page: () => HeaderAppbar(
            body: DuyetVbde(),
            title: AppString.duyetVbdeTile,
            isDrawer: true,
            pathScreen: _Paths.VBDEN),
        bindings: [DuyetVbdeBinding()]),
    GetPage(
        name: _Paths.DETAILVBDE,
        page: () => HeaderAppbar(
              body: DetailItemVbde(),
              title: AppString.chiTietVbdeTile,
              isDrawer: false,
              pathScreen: _Paths.DETAILVBDE,
            ),
        bindings: [ChiTietVbdeBinding()]),
    GetPage(
      name: _Paths.DUYETXULYVBDE,
      page: () => HeaderAppbar(
        body: DuyetXuLyVbde(),
        title: AppString.duyetVbdeTile,
        isDrawer: false,
        isConfirm: true,
        pathScreen: _Paths.DUYETXULYVBDE,
      ),
      bindings: [XulyVbdeBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.HOANTATVBDE,
      page: () => HeaderAppbar(
        body: HoanTatVbde(),
        title: AppString.themYKienXuLy,
        isDrawer: false,
        isConfirm: true,
        pathScreen: _Paths.HOANTATVBDE,
      ),
      bindings: [XulyVbdeBinding()],
      preventDuplicates: false,
    ),
    GetPage(
        name: _Paths.CHUYENLDKHAC,
        page: () => HeaderAppbar(
            body: ChuyenLanhDaoKhac(),
            title: AppString.chuyenLdKhac,
            isDrawer: false,
            isConfirm: true,
            pathScreen: _Paths.CHUYENLDKHAC),
        bindings: [XulyVbdeBinding()]),
    GetPage(
        name: _Paths.XULYCVVBDE,
        page: () => HeaderAppbar(
            body: XulyCvVbde(),
            title: AppString.xulycvVbde,
            isDrawer: true,
            pathScreen: _Paths.XULYCVVBDE),
        bindings: [DsCVVbdeBinding()]),
    GetPage(
        name: _Paths.CVCHUYENXL,
        page: () => HeaderAppbar(
              body: CvChuyenXLVbde(),
              title: AppString.chuyenVbde,
              isDrawer: false,
              isConfirm: true,
              pathScreen: _Paths.CVCHUYENXL,
            ),
        bindings: [CvXuLyVbdeBinding()]),
    GetPage(
        name: _Paths.CVNHAPYKIEN,
        page: () => HeaderAppbar(
              body: NhapYkienCV(),
              title: AppString.themYKienXuLy,
              isDrawer: false,
              isConfirm: true,
              pathScreen: _Paths.CVNHAPYKIEN,
            ),
        bindings: [CvXuLyVbdeBinding()]),
    GetPage(
        name: _Paths.SOVBDENVT,
        page: () => HeaderAppbar(
              body: SoVanBanDenVanThu(),
              title: AppString.sovbdenvt,
              isDrawer: true,
              pathScreen: _Paths.SOVBDENVT,
            ),
        bindings: [VanThuVbdeBinding()]),
    GetPage(
        name: _Paths.THEMMOIVBDEVT,
        page: () => HeaderAppbar(
              body: ThemMoiVbdeVt(),
              title: AppString.themVbdeVt,
              isDrawer: false,
              isConfirm: false,
              pathScreen: _Paths.THEMMOIVBDEVT,
            ),
        bindings: [VtXuLyVbdeBinding()]),
    GetPage(
      name: _Paths.VBDI,
      page: () => HeaderAppbar(
          body: ChoDuyetVbdi(),
          title: AppString.choDuyetVbdi,
          isDrawer: true,
          pathScreen: _Paths.VBDI),
      bindings: [VbdiChoDuyetBindings()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.DETAILSVBDI,
      page: () => HeaderAppbar(
          body: DetailItemVbdi(),
          title: AppString.chiTietVbdi,
          isDrawer: false,
          pathScreen: _Paths.DETAILSVBDI),
      bindings: [ChiTietVbdiBindings()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CHUYENLDKVBDI,
      page: () => HeaderAppbar(
          body: ChuyenLanhDaoKhacVbdi(),
          title: AppString.chuyenLdKhac,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.CHUYENLDKVBDI),
      bindings: [XuLyVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CHUYENCVVBDI,
      page: () => HeaderAppbar(
          body: ChuyenCvVbdi(),
          title: AppString.chuyenCvVbdi,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.CHUYENCVVBDI),
      bindings: [XuLyVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CHUYENVTVBDI,
      page: () => HeaderAppbar(
          body: ChuyenVtVbdi(),
          title: AppString.chuyenVtVbdi,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.CHUYENVTVBDI),
      bindings: [XuLyVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.DSCVVBDI,
      page: () => HeaderAppbar(
          body: DsXuLyCvVbdi(),
          title: AppString.xulyCvVbdi,
          isDrawer: true,
          pathScreen: _Paths.DSCVVBDI),
      bindings: [DsCvVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CVCHUYENCV,
      page: () => HeaderAppbar(
          body: CvChuyenCvVbdi(),
          title: AppString.chuyenCvVbdi,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.CVCHUYENCV),
      bindings: [XuLyVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CVCHUYENLD,
      page: () => HeaderAppbar(
          body: CvChuyenLd(),
          title: AppString.chuyenLdKhac,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.CVCHUYENLD),
      bindings: [XuLyVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.CVCHUYENVT,
      page: () => HeaderAppbar(
          body: CvChuyenVt(),
          title: AppString.chuyenVtVbdi,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.CVCHUYENVT),
      bindings: [XuLyVbdiBinding()],
      preventDuplicates: false,
    ),
    GetPage(
        name: _Paths.VBNB,
        page: () => HeaderAppbar(
            body: VanBanNoiBo(),
            title: AppString.vbnb,
            isDrawer: true,
            pathScreen: _Paths.VBNB),
        bindings: [VbnbBindings()]),
    GetPage(
        name: _Paths.VBNBDETAIL,
        page: () => HeaderAppbar(
            body: DetailItemVbnb(),
            title: AppString.detailVbnb,
            isDrawer: false,
            pathScreen: _Paths.VBNBDETAIL),
        binding: ChiTietVbnbBindings()),
    GetPage(
      name: _Paths.VBNBCHUYENXL,
      page: () => HeaderAppbar(
          body: CvChuyenXLVbnb(),
          title: AppString.chuyenVbnbXl,
          isDrawer: false,
          isConfirm: true,
          pathScreen: _Paths.VBNBCHUYENXL),
      bindings: [XuLyVbnbBindings()],
      preventDuplicates: false,
    ),
    GetPage(
      name: _Paths.KYSOKPI,
      page: () => ModalWebViewCa(),
      binding: KySoVbdiBinding(),
      preventDuplicates: false,
    ),
    GetPage(
        name: _Paths.THONGTINDIEUHANH,
        page: () => HeaderAppbar(
            body: ThongTinDieuHanh(),
            title: AppString.ttdh,
            isDrawer: true,
            isConfirm: true,
            pathScreen: _Paths.THONGTINDIEUHANH),
        bindings: [ThongTinDieuHanhBinding()]),
    GetPage(
        name: _Paths.THONGTINDIEUHANHDETAIL,
        page: () => HeaderAppbar(
            body: ThongTinDieuHanhDetail(),
            title: AppString.ttdhDetail,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.THONGTINDIEUHANHDETAIL),
        bindings: [TtdhetailBinding()]),
    GetPage(
        name: _Paths.TTDHPHANHOI,
        page: () => HeaderAppbar(
            body: TTDHPhanhoi(),
            title: AppString.ttdhPhanHoi,
            isDrawer: false,
            isConfirm: true,
            pathScreen: _Paths.TTDHPHANHOI),
        bindings: [TttdXuLyBinding()]),
    GetPage(
        name: _Paths.THEMTTDH,
        page: () => HeaderAppbar(
            body: TTDHPhanhoi(),
            title: AppString.ttdhThemMoi,
            isDrawer: false,
            isConfirm: true,
            pathScreen: _Paths.THEMTTDH),
        bindings: [TtdhetailBinding(), TttdXuLyBinding()]),
    GetPage(
        name: _Paths.TRACUUVBDI,
        page: () => HeaderAppbar(
            body: SearchCuuVanBanDi(),
            title: AppString.tracuuvbdi,
            isDrawer: true,
            pathScreen: _Paths.TRACUUVBDI),
        bindings: [TraCuuBinding()]),
    GetPage(
        name: _Paths.TRACUUVBDE,
        page: () => HeaderAppbar(
            body: SearchVanBanDen(),
            title: AppString.tracuuvbde,
            isDrawer: true,
            pathScreen: _Paths.TRACUUVBDE),
        bindings: [TraCuuVbdeBingding()]),
    GetPage(
        name: _Paths.TRACUUDETAILVBDI,
        page: () => HeaderAppbar(
            body: TraCuuDetailVbdi(),
            title: AppString.tracuudetail,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.TRACUUDETAILVBDI),
        bindings: [TraCuuDetailBinding()]),
    GetPage(
        name: _Paths.TRACUUDETAILVBDE,
        page: () => HeaderAppbar(
            body: TraCuuDetailVbde(),
            title: AppString.tracuudetail,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.TRACUUDETAILVBDE),
        bindings: [TraCuuDetailBinding()]),
    GetPage(
        name: _Paths.LICHCONGTAC,
        page: () => HeaderAppbar(
            body: LichCongTacView(),
            title: AppString.lichcongtac,
            isDrawer: true,
            isConfirm: false,
            pathScreen: _Paths.LICHCONGTAC),
        bindings: [LichCongTacBinding()]),
    GetPage(
        name: _Paths.TTCANHAN,
        page: () => HeaderAppbar(
            body: ThongTinCaNhan(),
            title: AppString.ttcanhan,
            isDrawer: true,
            isConfirm: false,
            pathScreen: _Paths.TTCANHAN),
        binding: UserBinding()),
    GetPage(
        name: _Paths.MAUCHUKY,
        page: () => HeaderAppbar(
            body: MauChuKy(),
            title: AppString.mauchuky,
            isDrawer: true,
            isConfirm: false,
            pathScreen: _Paths.MAUCHUKY),
        bindings: [MauChuKyBinding()]),
    GetPage(
        name: _Paths.DANHBA,
        page: () => HeaderAppbar(
            body: DanhBaView(),
            title: AppString.danhBa,
            isDrawer: true,
            isConfirm: false,
            pathScreen: _Paths.DANHBA),
        binding: DanhBaBinding()),
    GetPage(
        name: _Paths.THEODOIVBDE,
        page: () => HeaderAppbar(
            body: VbdeTheoDoiView(),
            title: AppString.theoDoiVbde,
            isDrawer: true,
            isConfirm: false,
            pathScreen: _Paths.THEODOIVBDE),
        binding: VbdeTheoDoiBinding()),
    GetPage(
        name: _Paths.LCTDETAIL,
        page: () => HeaderAppbar(
            body: DetailThuView(),
            title: AppString.lichcongtacDetai,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.LCTDETAIL),
        binding: LichCongTacDetailBinding()),
    GetPage(
        name: _Paths.LCTDETAILANHDAO,
        page: () => HeaderAppbar(
            body: DetailLctLanhDao(),
            title: AppString.lichcongtacDetai,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.LCTDETAILANHDAO),
        bindings: [LctLdDetailBinding()]),
    GetPage(
        name: _Paths.HOTRONGUOIDUNG,
        page: () => HeaderAppbar(
            body: HoTroNguoiDung(),
            title: AppString.thuHoiVanBan,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.HOTRONGUOIDUNG),
        bindings: [HoTroBinding()]),
    GetPage(
        name: _Paths.RESETMATKHAUNGUOIDUNG,
        page: () => HeaderAppbar(
            body: ResetMatKhauNguoiDung(),
            title: AppString.resetMatKhau,
            isDrawer: false,
            isConfirm: false,
            pathScreen: _Paths.RESETMATKHAUNGUOIDUNG),
        bindings: [ResetMkNguoiDungBinding()]),
  ];
}
