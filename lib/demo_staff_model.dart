import 'package:vnpt_ioffice_camau/app/model/hotro/staff_model.dart';

void main() {
  print('=== Demo StaffModel Usage ===\n');
  
  // 1. Demo tạo StaffModel từ JSON API response
  print('1. Tạo StaffModel từ JSON API response:');
  
  final apiResponseJson = {
    "success": true,
    "message": "Lấy dữ liệu thành công",
    "store_name": "CB_DS_CB_DV_BY_DVQT_HTND",
    "store_type": 2,
    "data": [
      {
        "ma_ctcb_kc": 4414.0,
        "ho_va_ten_can_bo": "Chủ tịch xã A",
        "username": "cntt.giamdoc",
        "ten_chuc_vu": "Chủ tịch",
        "sdt_can_bo": "0854999883",
        "di_dong_can_bo": "0854999883",
        "ten_don_vi": "<PERSON><PERSON><PERSON> đ<PERSON>",
        "ma_don_vi_kc": 1918.0
      },
      {
        "ma_ctcb_kc": 97843.0,
        "ho_va_ten_can_bo": "<PERSON>uyễn <PERSON>",
        "username": "cntt.nth",
        "ten_chuc_vu": "Chuyên viên",
        "sdt_can_bo": null,
        "di_dong_can_bo": "0",
        "ten_don_vi": "Các Chuyên viên",
        "ma_don_vi_kc": 21201.0
      },
      {
        "ma_ctcb_kc": 4413.0,
        "ho_va_ten_can_bo": "Phó Chủ tịch xã A",
        "username": "cntt.phogiamdoc",
        "ten_chuc_vu": "Phó Chủ tịch 1",
        "sdt_can_bo": "0",
        "di_dong_can_bo": "0945534099",
        "ten_don_vi": "Lãnh đạo",
        "ma_don_vi_kc": 1918.0
      }
    ]
  };

  // Parse API response
  final staffApiResponse = StaffApiResponse.fromJson(apiResponseJson);
  print('   - Success: ${staffApiResponse.success}');
  print('   - Message: ${staffApiResponse.message}');
  print('   - Store Name: ${staffApiResponse.storeName}');
  print('   - Data count: ${staffApiResponse.data.length}');

  print('\n2. Chi tiết từng cán bộ:');
  for (int i = 0; i < staffApiResponse.data.length; i++) {
    final staff = staffApiResponse.data[i];
    print('   Cán bộ ${i + 1}:');
    print('     - ID: ${staff.maCtcbKc}');
    print('     - Họ tên: ${staff.hoVaTenCanBo}');
    print('     - Username: ${staff.username}');
    print('     - Chức vụ: ${staff.tenChucVu}');
    print('     - Đơn vị: ${staff.tenDonVi}');
    print('     - SĐT: ${staff.getPhoneNumber()}');
    print('     - Có SĐT: ${staff.hasPhoneNumber()}');
    print('');
  }

  print('3. Demo các method utility:');
  final staff1 = staffApiResponse.data[0];
  
  // toOldFormat
  print('   - Old format: ${staff1.toOldFormat()}');
  
  // copyWith
  final updatedStaff = staff1.copyWith(
    hoVaTenCanBo: 'Tên mới',
    tenChucVu: 'Chức vụ mới'
  );
  print('   - Updated staff: ${updatedStaff.hoVaTenCanBo} - ${updatedStaff.tenChucVu}');
  
  // toString
  print('   - toString: $staff1');

  print('\n4. Demo toJson:');
  final jsonOutput = staff1.toJson();
  print('   JSON output: $jsonOutput');

  print('\n5. Demo equality:');
  final staff1Copy = StaffModel.fromJson(staff1.toJson());
  print('   - staff1 == staff1Copy: ${staff1 == staff1Copy}');
  print('   - staff1.hashCode: ${staff1.hashCode}');
  print('   - staff1Copy.hashCode: ${staff1Copy.hashCode}');

  print('\n6. Demo xử lý số điện thoại:');
  final staffWithoutPhone = StaffModel(
    maCtcbKc: 999,
    hoVaTenCanBo: 'Test User',
    username: 'test.user',
    tenChucVu: 'Test Position',
    sdtCanBo: null,
    diDongCanBo: '0',
    tenDonVi: 'Test Unit',
    maDonViKc: 1,
  );
  
  print('   - Staff without phone: ${staffWithoutPhone.getPhoneNumber()}');
  print('   - Has phone: ${staffWithoutPhone.hasPhoneNumber()}');

  final staffWithMobileOnly = StaffModel(
    maCtcbKc: 998,
    hoVaTenCanBo: 'Mobile User',
    username: 'mobile.user',
    tenChucVu: 'Mobile Position',
    sdtCanBo: '0',
    diDongCanBo: '0987654321',
    tenDonVi: 'Mobile Unit',
    maDonViKc: 2,
  );
  
  print('   - Staff with mobile only: ${staffWithMobileOnly.getPhoneNumber()}');
  print('   - Has phone: ${staffWithMobileOnly.hasPhoneNumber()}');

  print('\n=== Demo completed ===');
}

// Ví dụ sử dụng trong service
class StaffService {
  Future<List<StaffModel>> getStaffByUnit(int unitId) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock API response
      final response = {
        "success": true,
        "message": "Lấy dữ liệu thành công",
        "store_name": "CB_DS_CB_DV_BY_DVQT_HTND",
        "store_type": 2,
        "data": [
          {
            "ma_ctcb_kc": 4414.0,
            "ho_va_ten_can_bo": "Chủ tịch xã A",
            "username": "cntt.giamdoc",
            "ten_chuc_vu": "Chủ tịch",
            "sdt_can_bo": "0854999883",
            "di_dong_can_bo": "0854999883",
            "ten_don_vi": "Lãnh đạo",
            "ma_don_vi_kc": unitId.toDouble()
          }
        ]
      };

      final staffApiResponse = StaffApiResponse.fromJson(response);
      return staffApiResponse.data;
    } catch (e) {
      print('Error fetching staff: $e');
      return [];
    }
  }

  Future<bool> resetPassword(StaffModel staff) async {
    try {
      // Convert to JSON for API
      final jsonData = staff.toJson();
      
      print('Resetting password for: ${jsonData['username']}');
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      return true;
    } catch (e) {
      print('Error resetting password: $e');
      return false;
    }
  }
}
